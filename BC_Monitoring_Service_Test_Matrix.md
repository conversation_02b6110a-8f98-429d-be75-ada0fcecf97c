# BC Monitoring Service — End-to-End Test Matrix

## 1. Service Initialization & Startup
### 1.1 Normal Cases
#### 1.1.1 Successful Service Startup
- **Description**: Service starts successfully with all dependencies available
- **Input**: Valid environment variables, accessible S3 bucket with valid ABI files, accessible DynamoDB, accessible Ethereum WebSocket endpoint
- **Expected Result**: Service logs "started bc monitoring", begins monitoring blockchain events
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/cmd/bc_monitoring/main.go (lines 22-33)
  - Functions: main(), initializeDownloadAbiInteractor(), initializeMonitorInteractor()
  - Key Implementation: Log messages "starting bc monitoring" and "started bc monitoring"
  - Line Numbers: 23 (startup log), 33 (completion log)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/MonitoringRunnerConfig.java (lines 35-59)
  - Functions: commandLineRunner(), downloadAbiService.execute(), monitorEventService.execute()
  - Key Implementation: Log messages "Starting bc monitoring" and "Started bc monitoring"
  - Line Numbers: 37 (startup log), 53 (completion log)

#### 1.1.2 Service Restart After WebSocket Error
- **Description**: Service automatically restarts monitoring after WebSocket handshake error
- **Input**: WebSocket connection fails with "rpc.wsHandshakeError"
- **Expected Result**: Service logs "restart bc monitoring", reinitializes monitor interactor, continues operation
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/cmd/bc_monitoring/main.go (lines 34-46)
  - Functions: retry.Do() with RetryIf condition, OnRetry callback
  - Key Implementation: Specific error check `err.Error() == "rpc.wsHandshakeError"`, retry configuration with 5 attempts and 3-nanosecond delays
  - Line Numbers: 37 (error condition), 39 (restart log), 10-16 (retry configuration)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/RetryConfig.java (lines 20-34)
  - Functions: retryTemplate(), SimpleRetryPolicy(), FixedBackOffPolicy()
  - Key Implementation: WebSocketHandshakeException.class retry policy, 5 attempts with 3000ms backoff period
  - Line Numbers: 24 (retry policy), 27 (backoff period), 30 (retry listener)

### 1.2 Semi-Normal Cases
#### 1.2.1 Service Startup with Empty ABI Bucket
- **Description**: Service starts when S3 bucket exists but contains no ABI files
- **Input**: Empty S3 bucket, valid other dependencies
- **Expected Result**: Service starts successfully with no contract addresses loaded, monitoring starts but no events will be detected since no ABI definitions are available
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go (lines 32-74)
  - Functions: Execute(), parseAbiContent()
  - Key Implementation: Empty CommonPrefixes list results in no ABI processing, contractEventStore remains empty
  - Line Numbers: 36-46 (S3 listing), 41-46 (prefix iteration)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/abi/DownloadAbiService.java (lines 48-123)
  - Functions: execute(), listCommonPrefixesObjects()
  - Key Implementation: Empty commonPrefixes list results in no ABI processing, contractEventStore remains empty
  - Line Numbers: 59-67 (S3 listing), 69-122 (prefix iteration)

#### 1.2.2 Service Startup with Missing Block Height Record
- **Description**: Service starts when DynamoDB BlockHeight table is empty
- **Input**: Empty BlockHeight table in DynamoDB
- **Expected Result**: Service starts monitoring from block 1 (blockHeight 0 + 1)
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/adapter/block_height.go (lines 32-44)
  - Functions: Get()
  - Key Implementation: Returns BlockHeight{BlockNumber: 0} when no records found, monitoring starts from blockHeight + 1
  - Line Numbers: 38-42 (empty table handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/dynamodb/BlockHeightDao.java (lines 41-73)
  - Functions: get()
  - Key Implementation: Returns 0L when blockHeights.isEmpty(), monitoring starts from blockHeight + 1
  - Line Numbers: 63-65 (empty table handling)

### 1.3 Abnormal Cases
#### 1.3.1 Service Startup Failure - Invalid Environment Variables
- **Description**: Service fails to start due to missing or invalid environment variables
- **Input**: Missing WEBSOCKET_URI_HOST, WEBSOCKET_URI_PORT, or other required env vars
- **Expected Result**: Service exits with fatal error, logs configuration error
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/config/config.go (lines 19-24)
  - Functions: GetWebsocketURIHost(), GetWebsocketURIPort()
  - Key Implementation: Environment variable retrieval via os.Getenv(), failures propagate to fatal errors
  - Line Numbers: 19-24 (WebSocket config), 7-16 (DynamoDB config)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/resources/application.properties (lines 21-22)
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/BcmonitoringConfigurationProperties.java (lines 69-77)
  - Functions: Spring Boot configuration binding, @ConfigurationProperties
  - Key Implementation: Environment variable binding with default values, Spring Boot startup validation
  - Line Numbers: 21-22 (WebSocket config), 6-15 (DynamoDB config)

#### 1.3.2 Service Startup Failure - DynamoDB Connection Error
- **Description**: Service fails to start due to DynamoDB connection issues
- **Input**: Invalid DynamoDB endpoint or credentials
- **Expected Result**: Service exits with fatal error during monitor interactor initialization
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/dynamodb/dynamodb.go (lines 21-44)
  - Functions: newConnection(), clientPool.New()
  - Key Implementation: DynamoDB client initialization with endpoint resolver, connection pool management
  - Line Numbers: 25-36 (environment-based config), 46-57 (connection handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/DynamoDBConfig.java (lines 24-39)
  - Functions: dynamoDbClient(), DynamoDbClient.builder()
  - Key Implementation: DynamoDB client initialization with environment-based configuration, Spring Boot bean creation
  - Line Numbers: 26-31 (local config), 33-38 (production config)

## 2. ABI File Management
### 2.1 Normal Cases
#### 2.1.1 Successful ABI Download and Parsing
- **Description**: Downloads and parses valid ABI files from S3
- **Input**: S3 bucket with valid JSON ABI files in correct directory structure (e.g., "3000/Contract.json")
- **Expected Result**: ABI files parsed successfully, contract addresses and events stored in memory
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go (lines 76-116)
  - Functions: parseAbiContent(), abi.UnmarshalJSON()
  - Key Implementation: JSON parsing with gjson, ABI unmarshaling, storage in ethereum.contractEventStore
  - Line Numbers: 96-102 (ABI parsing), 105-106 (storage)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/AbiParser.java (lines 127-226)
  - Functions: parseAbiContent(), parseAbi(), parseAndRegisterEvents()
  - Key Implementation: JSON parsing with Jackson, Web3j Event creation, storage in contractEventStore
  - Line Numbers: 140-156 (ABI parsing), 222-225 (storage)

#### 2.1.2 Multiple Zone ABI Processing
- **Description**: Processes ABI files from multiple zones (3000, 3001, etc.)
- **Input**: S3 bucket with ABI files in multiple zone directories
- **Expected Result**: All zone ABI files processed, contracts from all zones available for monitoring
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go (lines 41-46)
  - Functions: Execute() with CommonPrefixes iteration
  - Key Implementation: Iterates through all S3 CommonPrefixes (zone directories), processes each zone separately
  - Line Numbers: 41-46 (prefix iteration), 43 (zone-specific listing)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/abi/DownloadAbiService.java (lines 69-122)
  - Functions: execute() with commonPrefixes iteration
  - Key Implementation: Iterates through all S3 CommonPrefixes (zone directories), processes each zone separately
  - Line Numbers: 69-122 (prefix iteration), 76-77 (zone-specific listing)

#### 2.1.3 Hardhat vs Truffle ABI Format Handling
- **Description**: Correctly parses ABI files based on ABI_FORMAT environment variable
- **Input**: ABI files with different address field locations, ABI_FORMAT set to "hardhat" or "truffle"
- **Expected Result**: Contract addresses extracted from correct JSON path based on format
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go (lines 87-94)
  - Functions: parseAbiContent() with format branching
  - Key Implementation: Conditional address extraction: truffle uses "networks.*.address", hardhat uses "address"
  - Line Numbers: 87-94 (format detection and address extraction)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/AbiParser.java (lines 137-148)
  - Functions: parseAbiContent() with format branching
  - Key Implementation: Conditional address extraction: truffle uses findFirstAddressInNetworks(), hardhat uses "address" field
  - Line Numbers: 141-148 (format detection and address extraction)

### 2.2 Semi-Normal Cases
#### 2.2.1 Non-JSON Files in S3 Bucket
- **Description**: Skips non-JSON files in S3 bucket
- **Input**: S3 bucket containing .txt, .md, or other non-.json files
- **Expected Result**: Non-JSON files skipped with log message, JSON files processed normally
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go (lines 54-57)
  - Functions: Execute() with file extension check
  - Key Implementation: filepath.Ext(objKey) != ".json" check, skip message printed to stdout
  - Line Numbers: 54-57 (extension validation and skip logic)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/abi/DownloadAbiService.java (lines 94-102)
  - Functions: execute() with file extension check, getFileExtension()
  - Key Implementation: !".json".equals(extension) check, skip message logged via logger.info()
  - Line Numbers: 99-102 (extension validation and skip logic)

#### 2.2.2 Nested Directory Structure
- **Description**: Processes only direct subdirectory files, skips deeply nested files
- **Input**: S3 bucket with files in nested directories (e.g., "3000/subdir/Contract.json")
- **Expected Result**: Deeply nested files skipped, only direct zone subdirectory files processed
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go (lines 50-53)
  - Functions: Execute() with directory depth check
  - Key Implementation: strings.Count(objKey, "/") > 1 check to skip deeply nested files
  - Line Numbers: 50-53 (nested directory filtering)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/abi/DownloadAbiService.java (lines 89-92)
  - Functions: execute() with directory depth check
  - Key Implementation: objKey.chars().filter(ch -> ch == '/').count() > 1 check to skip deeply nested files
  - Line Numbers: 89-92 (nested directory filtering)

### 2.3 Abnormal Cases
#### 2.3.1 S3 Bucket Access Denied
- **Description**: Service fails to start when S3 bucket is inaccessible
- **Input**: S3 bucket with access denied or invalid credentials
- **Expected Result**: Service exits with fatal error "failed to list s3 CommonPrefixes objects"
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go (lines 36-38)
  - Functions: Execute(), ListCommonPrefixesObjects()
  - Key Implementation: Error message "failed to list s3 CommonPrefixes objects %v\n"
  - Line Numbers: 36-38 (S3 access error handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/S3ClientAdaptor.java (lines 32-43)
  - Functions: listCommonPrefixesObjects(), S3Exception handling
  - Key Implementation: Error message "Error listing common prefixes in bucket", throws S3CommonPrefixesListingException
  - Line Numbers: 39-42 (S3 access error handling)

#### 2.3.2 Invalid JSON File Content
- **Description**: Service fails to start when ABI file contains malformed JSON
- **Input**: S3 bucket with corrupted or invalid JSON files
- **Expected Result**: Service exits with fatal error "failed to unmarshal abi json"
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go (lines 98-102)
  - Functions: parseAbiContent(), abi.UnmarshalJSON()
  - Key Implementation: Error message "failed to unmarshal abi json: %s. %v\n"
  - Line Numbers: 98-102 (ABI unmarshaling error handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/AbiParser.java (lines 91-94)
  - Functions: parseAbi(), IOException handling
  - Key Implementation: Error message "ABI parsing failed", throws IOException with cause
  - Line Numbers: 91-94 (ABI parsing error handling)

#### 2.3.3 Missing ABI Field in JSON
- **Description**: Service fails when JSON file lacks required "abi" field
- **Input**: Valid JSON file without "abi" field or with empty "abi" field
- **Expected Result**: Service exits with fatal error during ABI unmarshaling
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go (lines 96-102)
  - Functions: parseAbiContent() with gjson.GetBytes()
  - Key Implementation: gjson.GetBytes(abiJson, "abi") extraction, fails if field missing
  - Line Numbers: 97 (ABI field extraction), 98-102 (unmarshaling error)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/AbiParser.java (lines 153-156)
  - Functions: parseAbiContent() with JsonNode.path()
  - Key Implementation: abiNode.isMissingNode() check, throws IOException "ABI section not found in JSON"
  - Line Numbers: 154-156 (ABI field validation)

#### 2.3.4 S3 Network Timeout
- **Description**: Service fails when S3 operations timeout
- **Input**: S3 operations that exceed timeout limits
- **Expected Result**: Service exits with fatal error, timeout error logged
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/s3/abi.go (lines 21-52)
  - Functions: ListCommonPrefixesObjects(), ListObjects(), GetObject()
  - Key Implementation: S3 client operations with context.TODO(), timeout errors propagated
  - Line Numbers: 22-29 (S3 operations), error handling in calling functions
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/S3Config.java (lines 43-45)
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/S3ClientAdaptor.java (lines 77-89)
  - Functions: s3Client() with RetryPolicy, S3Exception handling
  - Key Implementation: ClientOverrideConfiguration with RetryPolicy.defaultRetryPolicy(), timeout errors propagated as S3Exception
  - Line Numbers: 43-45 (retry policy), 82-86 (timeout error handling)

## 3. Blockchain Event Monitoring
### 3.1 Normal Cases
#### 3.1.1 New Block Event Detection
- **Description**: Detects and processes events from new blockchain blocks
- **Input**: New blocks with contract events matching loaded ABI definitions
- **Expected Result**: Events extracted, parsed, and saved to DynamoDB with correct transaction hash, log index, and event data
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go (lines 40-100)
  - Functions: SubscribeAll(), convBlock2EventEntities()
  - Key Implementation: WebSocket subscription to new block headers, event extraction and parsing
  - Line Numbers: 42-48 (subscription setup), 67-95 (block processing)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java (lines 76-137)
  - Functions: subscribeAll(), convBlock2EventEntities()
  - Key Implementation: Web3j blockFlowable subscription, event extraction and parsing
  - Line Numbers: 94-96 (subscription setup), 110-119 (block processing)

#### 3.1.2 Pending Transaction Processing
- **Description**: Processes pending transactions from specified block height
- **Input**: Pending transactions in blockchain from last processed block height + 1
- **Expected Result**: Pending transactions processed, events saved, block height updated
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go (lines 198-240)
  - Functions: GetPendingTransactions(), FilterLogs()
  - Key Implementation: FilterLogs with blockHeight parameter, processes historical events
  - Line Numbers: 205-210 (filter logs), 220-235 (event processing)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java (lines 320-386)
  - Functions: getPendingTransactions(), ethGetLogs()
  - Key Implementation: EthFilter with fromBlock parameter, processes historical events
  - Line Numbers: 340-350 (filter logs), 359-385 (event processing)

#### 3.1.3 Event Data Parsing and Storage
- **Description**: Correctly parses indexed and non-indexed event parameters
- **Input**: Contract events with various parameter types (indexed/non-indexed)
- **Expected Result**: Event data correctly parsed into IndexedValues and NonIndexedValues JSON strings
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go (lines 136-196)
  - Functions: convertEthLogToEventEntity(), abi.Arguments.UnpackValues()
  - Key Implementation: Separate processing of indexed and non-indexed arguments, JSON marshaling
  - Line Numbers: 155-161 (indexed args), 162-185 (non-indexed args), 187-196 (entity creation)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java (lines 219-287)
  - Functions: convertEthLogToEventEntity(), FunctionReturnDecoder.decode()
  - Key Implementation: Separate processing of indexed and non-indexed arguments, JSON marshaling with ObjectMapper
  - Line Numbers: 240-250 (indexed args), 251-268 (non-indexed args), 274-282 (entity creation)

#### 3.1.4 TraceId Extraction
- **Description**: Extracts traceId from event non-indexed values when present
- **Input**: Events containing traceId in non-indexed values
- **Expected Result**: TraceId correctly extracted and logged with event processing
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 176-194)
  - Functions: fetchTraceId(), json.Unmarshal()
  - Key Implementation: JSON parsing of NonIndexedValues, traceId field extraction with null byte filtering
  - Line Numbers: 176-194 (traceId extraction logic)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/blockchain/TraceIdExtractorService.java (lines 1-50)
  - Functions: fetchTraceId(), ObjectMapper.readTree()
  - Key Implementation: JSON parsing of NonIndexedValues, traceId field extraction with JsonNode
  - Line Numbers: 20-40 (traceId extraction logic)

### 3.2 Semi-Normal Cases
#### 3.2.1 Block with No Matching Events
- **Description**: Processes blocks that contain no events matching loaded ABIs
- **Input**: Blockchain blocks with transactions but no events matching contract addresses
- **Expected Result**: Block processed without errors, no events saved, block height updated
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go (lines 80-96)
  - Functions: convBlock2EventEntities() returning nil
  - Key Implementation: When no matching events found, es == nil, no transaction sent to channel
  - Line Numbers: 80-86 (event processing), 87-95 (conditional transaction sending)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java (lines 110-119)
  - Functions: convBlock2EventEntities() returning empty list
  - Key Implementation: When no matching events found, !events.isEmpty() check prevents transaction creation
  - Line Numbers: 110-111 (event processing), 111-119 (conditional transaction creation)

#### 3.2.2 Event Without TraceId
- **Description**: Processes events that don't contain traceId in non-indexed values
- **Input**: Contract events without traceId field
- **Expected Result**: Event processed normally, empty traceId logged
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 176-194)
  - Functions: fetchTraceId() with error handling
  - Key Implementation: Returns empty string when traceId field missing or parsing fails
  - Line Numbers: 178-182 (error handling), 183-185 (nil/empty check)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/blockchain/TraceIdExtractorService.java (lines 31-52)
  - Functions: fetchTraceId() with error handling
  - Key Implementation: Returns empty string when traceId field missing or parsing fails
  - Line Numbers: 35-37 (null/empty check), 48-51 (error handling)

#### 3.2.3 Block Timestamp Validation
- **Description**: Validates block timestamps against allowable difference threshold
- **Input**: Blocks with timestamps within ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC
- **Expected Result**: Blocks processed normally when timestamp difference is acceptable
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go (lines 49, 73-79)
  - Functions: SubscribeAll() with isDelayedToDetectBlockHeader()
  - Key Implementation: Timestamp difference calculation, warning log when delayed but processing continues
  - Line Numbers: 49 (allowableDiff config), 73-79 (timestamp validation)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java (lines 146-152)
  - Functions: isDelayed()
  - Key Implementation: Timestamp comparison with allowable difference, block skipping logic
  - Line Numbers: 103-107 (delay check), 146-152 (timestamp validation)

#### 3.2.4 ABI Event Definition Not Found
- **Description**: Handles events that don't match any loaded ABI definitions
- **Input**: Blockchain events from contracts not in loaded ABI store
- **Expected Result**: ErrNoAbiEventFound error (not logged), event skipped, monitoring continues
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/ethereum.go (lines 27, 54-63)
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go (lines 82-84)
  - Functions: getABIEventByLog(), error handling in SubscribeAll()
  - Key Implementation: ErrNoAbiEventFound defined but not logged, errors.Is() check
  - Line Numbers: 27 (error definition), 82-84 (error handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java (lines 233-237)
  - Functions: convertEthLogToEventEntity(), abiParser.getABIEventByLog()
  - Key Implementation: Exception thrown when event definition not found, error logged and event skipped
  - Line Numbers: 234-237 (ABI event lookup and error handling)

### 3.3 Abnormal Cases
#### 3.3.1 WebSocket Connection Failure
- **Description**: Handles WebSocket connection failures during monitoring
- **Input**: WebSocket connection drops or fails during event subscription
- **Expected Result**: Error logged, monitoring stops, service attempts restart via retry mechanism
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go (lines 64-66)
  - Functions: SubscribeAll() error channel handling
  - Key Implementation: subscribe.Err() channel monitoring, error logging and return
  - Line Numbers: 64-66 (subscription error handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java (lines 125-128)
  - Functions: subscribeAll() error callback handling
  - Key Implementation: Web3j subscription error callback, error logging and Web3j shutdown
  - Line Numbers: 125-128 (subscription error handling)

#### 3.3.2 Event with Empty Transaction Hash
- **Description**: Rejects events with empty or missing transaction hash
- **Input**: Blockchain events with empty transaction hash
- **Expected Result**: Event rejected, error logged "event transaction hash is zero", monitoring continues with retry
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 112-115)
  - Functions: saveTransaction(), savePendingTransaction()
  - Key Implementation: TransactionHash == "" check, exact error message "event transaction hash is zero"
  - Line Numbers: 112-115 (transaction hash validation), 141-144 (pending transaction validation)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java (lines 223-226)
  - Functions: saveTransaction(), savePendingTransaction()
  - Key Implementation: transactionHash.isEmpty() check, exact error message "Event transaction hash is zero"
  - Line Numbers: 223-226 (transaction hash validation), 262-265 (pending transaction validation)

#### 3.3.3 Block Number Zero Detection
- **Description**: Handles blocks with zero block number
- **Input**: Blockchain events with BlockNumber = 0
- **Expected Result**: Block rejected, warning logged "block height Number is zero" (WARN level), monitoring restarts after interval using goto RETRY pattern
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 88-92, 70-74)
  - Functions: Execute() monitoring loop with goto RETRY
  - Key Implementation: BlockNumber == 0 check, exact warning message, goto RETRY pattern with sleep
  - Line Numbers: 88-92 (transaction processing), 70-74 (pending transaction processing)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java (lines 197-200)
  - Functions: processNewTransactions(), processPendingTransactions()
  - Key Implementation: blockHeight.blockNumber == 0 check, exact warning message "Block height Number is zero", method return
  - Line Numbers: 197-200 (transaction processing), 158-163 (pending transaction processing)

## 4. Data Persistence
### 4.1 Normal Cases
#### 4.1.1 Event Storage to DynamoDB
- **Description**: Successfully stores parsed events to DynamoDB Events table
- **Input**: Valid parsed events with all required fields
- **Expected Result**: Events stored in DynamoDB with correct table name prefix, success logged
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/dynamodb/events.go (lines 18-31)
  - Functions: PutItem(), serializePutItemInput()
  - Key Implementation: DynamoDB PutItem operation with connection pooling, table name prefixing
  - Line Numbers: 18-31 (event storage), 69-88 (serialization with table prefix)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/dynamodb/EventDao.java (lines 35-44)
  - Functions: save(), event.toAttributeMap()
  - Key Implementation: DynamoDB putItem operation with Spring-managed client, table name prefixing
  - Line Numbers: 39-43 (event storage), 41 (table name with prefix)

#### 4.1.2 Block Height Update
- **Description**: Updates block height after successful event processing
- **Input**: Successfully processed block with events
- **Expected Result**: Block height updated in DynamoDB BlockHeight table with ID=1
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/dynamodb/block_height.go (lines 23-36)
  - Functions: PutItem() with ID=1 assignment
  - Key Implementation: blockHeight.ID = 1 assignment, DynamoDB PutItem operation
  - Line Numbers: 29 (ID assignment), 23-36 (block height storage)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/dynamodb/BlockHeightDao.java (lines 104-110)
  - Functions: save(), blockHeight.toAttributeMap()
  - Key Implementation: DynamoDB putItem operation with Spring-managed client, table name prefixing
  - Line Numbers: 105-109 (block height storage), 108 (table name with prefix)

#### 4.1.3 Concurrent Event Processing
- **Description**: Handles multiple events from same transaction/block
- **Input**: Multiple events from single transaction or block
- **Expected Result**: All events processed and stored individually with correct log indexes
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 108-130)
  - Functions: saveTransaction() with event iteration
  - Key Implementation: for _, e := range tx.Events loop, individual event processing
  - Line Numbers: 111-130 (event iteration and storage)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java (lines 221-244)
  - Functions: saveTransaction() with event iteration
  - Key Implementation: for (Event e : tx.events) loop, individual event storage
  - Line Numbers: 222-244 (event iteration and storage)

#### 4.1.4 Pending vs Regular Transaction Processing
- **Description**: Different block height update behavior for pending vs regular transactions
- **Input**: Mix of pending transactions and regular blockchain events
- **Expected Result**: Pending transactions save events but do NOT update block height; regular transactions save events AND update block height
- **Source Code**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 108-136, 139-161)
  - Functions: saveTransaction() vs savePendingTransaction()
  - Key Implementation: saveTransaction() calls BlockHeightRepository.Save(), savePendingTransaction() does not
  - Line Numbers: 131-136 (regular transaction block height update), 139-161 (pending transaction without block height update)

### 4.2 Semi-Normal Cases
#### 4.2.1 DynamoDB Connection Pool Management
- **Description**: Manages DynamoDB connection pool under load
- **Input**: High volume of events requiring database operations
- **Expected Result**: Connection pool (max 10 connections) managed correctly, operations complete successfully
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/dynamodb/dynamodb.go (lines 19, 46-67)
  - Functions: newConnection(), releaseConnection()
  - Key Implementation: Semaphore with max 10 connections, sync.Pool for connection reuse
  - Line Numbers: 19 (semaphore definition), 46-67 (connection management)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/DynamoDBConfig.java (lines 24-39)
  - Functions: dynamoDbClient() Spring Bean
  - Key Implementation: Spring-managed DynamoDbClient with automatic connection pooling and lifecycle management
  - Line Numbers: 25-39 (client configuration and bean creation)

#### 4.2.2 Empty Events List Processing
- **Description**: Handles transactions with empty events list
- **Input**: Transactions containing empty Events array
- **Expected Result**: Transaction processed without errors, block height updated, no events stored
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 111-136)
  - Functions: saveTransaction() with empty events loop
  - Key Implementation: for _, e := range tx.Events loop handles empty slice, block height still updated
  - Line Numbers: 111 (events loop), 131-136 (block height update regardless)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java (lines 221-252)
  - Functions: saveTransaction() with empty events loop
  - Key Implementation: for (Event e : tx.events) loop handles empty collection, block height still updated
  - Line Numbers: 222 (events loop), 245-252 (block height update regardless)

### 4.3 Abnormal Cases
#### 4.3.1 DynamoDB Write Failure
- **Description**: Handles DynamoDB write failures during event storage
- **Input**: DynamoDB PutItem operations that fail due to service issues
- **Expected Result**: Event save returns false, error logged, monitoring stops and retries after interval
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/adapter/events.go (lines 22-29)
  - Functions: Save() with error handling
  - Key Implementation: Returns false on PutItem error, error logged via repo.l.Errorln(err)
  - Line Numbers: 23-28 (error handling and return false)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/dynamodb/EventDao.java (lines 45-49)
  - Functions: save() with DynamoDbException handling
  - Key Implementation: Returns false on DynamoDbException, error logged via logger.error()
  - Line Numbers: 45-49 (exception handling and return false)

#### 4.3.2 DynamoDB Read Failure
- **Description**: Handles DynamoDB read failures when getting block height
- **Input**: DynamoDB Query operations that fail when retrieving current block height
- **Expected Result**: Error logged "failed to get blockheight", monitoring stops and returns error
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 42-46)
  - Functions: Execute() with BlockHeightRepository.Get()
  - Key Implementation: Error message "failed to get blockheight %s", returns error to stop monitoring
  - Line Numbers: 42-46 (block height retrieval error handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/dynamodb/BlockHeightDao.java (lines 68-72)
  - Functions: get() with DynamoDbException handling
  - Key Implementation: Throws DataAccessException on DynamoDbException, error logged
  - Line Numbers: 68-72 (exception handling and DataAccessException throw)

#### 4.3.3 DynamoDB Serialization Error
- **Description**: Handles errors during DynamoDB attribute serialization
- **Input**: Event or BlockHeight entities that cannot be serialized to DynamoDB format
- **Expected Result**: Serialization error logged, operation fails, monitoring stops
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/dynamodb/dynamodb.go (lines 69-73)
  - Functions: serializePutItemInput() with attributevalue.MarshalMap()
  - Key Implementation: attributevalue.MarshalMap() error handling, returns error on serialization failure
  - Line Numbers: 70-73 (serialization error handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/Event.java (lines in toAttributeMap())
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/domain/model/BlockHeight.java (lines in toAttributeMap())
  - Functions: toAttributeMap() methods with AttributeValue.builder()
  - Key Implementation: Direct AttributeValue creation, exceptions handled at DAO level
  - Line Numbers: Entity serialization methods (specific lines depend on implementation)

#### 4.3.4 Block Height Save Failure
- **Description**: Handles failures when updating block height
- **Input**: DynamoDB PutItem failure for BlockHeight entity
- **Expected Result**: Block height save returns false, error logged, monitoring stops and retries after interval
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/adapter/block_height.go (lines 23-30)
  - Functions: Save() with error handling
  - Key Implementation: Returns false on PutItem error, error logged via repo.l.Errorln(err)
  - Line Numbers: 24-29 (error handling and return false)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/dynamodb/BlockHeightDao.java (lines 111-117)
  - Functions: save() with DynamoDbException handling
  - Key Implementation: Returns false on DynamoDbException, error logged via logger.error()
  - Line Numbers: 111-117 (exception handling and return false)

## 5. Configuration Management
### 5.1 Normal Cases
#### 5.1.1 Environment Variable Loading
- **Description**: Correctly loads all required environment variables
- **Input**: All required environment variables set with valid values
- **Expected Result**: Configuration values accessible throughout application
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/config/config.go (lines 7-51)
  - Functions: GetDynamoDBEndpoint(), GetWebsocketURIHost(), GetS3BucketName(), etc.
  - Key Implementation: os.Getenv() calls for all configuration parameters
  - Line Numbers: 7-51 (all environment variable getters)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/BcmonitoringConfigurationProperties.java (lines 1-92)
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/resources/application.properties (lines 1-39)
  - Functions: @ConfigurationProperties binding, Spring Boot property resolution
  - Key Implementation: Spring Boot property binding with default values and environment variable overrides
  - Line Numbers: 9 (@ConfigurationProperties), 6-30 (environment variable mappings)

#### 5.1.2 Local vs Production Environment Handling
- **Description**: Correctly configures services based on ENV variable
- **Input**: ENV set to "local" or "prod"
- **Expected Result**: Appropriate S3 and DynamoDB endpoints configured
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/s3/s3.go (lines 16-37)
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/dynamodb/dynamodb.go (lines 25-36)
  - Functions: newS3Client(), clientPool.New()
  - Key Implementation: Environment-based branching for local vs prod configurations
  - Line Numbers: 16-37 (S3 config), 25-36 (DynamoDB config)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/AwsCredentialsConfig.java (lines 35-53)
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/DynamoDBConfig.java (lines 25-39)
  - Functions: awsCredentialsProvider(), dynamoDbClient()
  - Key Implementation: Environment-based configuration with LOCAL, PROD, and other environment handling
  - Line Numbers: 36-53 (credentials config), 26-38 (DynamoDB config)

### 5.2 Semi-Normal Cases
#### 5.2.1 Default Configuration Values
- **Description**: Uses default values when optional environment variables are missing
- **Input**: Missing optional configuration like table name prefix
- **Expected Result**: Service uses default values, operates normally
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/config/local_config.go (lines 9-13)
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/dynamodb/dynamodb.go (lines 76-81)
  - Functions: GetLocalConfig() with envconfig defaults, table name handling
  - Key Implementation: envconfig default tags, empty string checks for optional configs
  - Line Numbers: 10-12 (default values), 76-81 (table prefix handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/resources/application.properties (lines 6-36)
  - Functions: Spring Boot property resolution with default values
  - Key Implementation: ${ENV_VAR:default_value} syntax for environment variable overrides with defaults
  - Line Numbers: 6-36 (all property definitions with defaults)

### 5.3 Abnormal Cases
#### 5.3.1 Invalid Subscription Check Interval
- **Description**: Handles invalid SUBSCRIPTION_CHECK_INTERVAL value
- **Input**: SUBSCRIPTION_CHECK_INTERVAL set to non-numeric value
- **Expected Result**: Service fails with error "faild to convert checkInterval" (note: typo in actual code), monitoring stops
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 35-38)
  - Functions: Execute() with strconv.Atoi()
  - Key Implementation: strconv.Atoi() conversion, exact error message with typo "faild to convert checkInterval"
  - Line Numbers: 35-38 (conversion and error handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/resources/application.properties (line 25)
  - Functions: Spring Boot property binding with Integer.parseInt()
  - Key Implementation: Spring Boot validates numeric properties at startup, fails with PropertyConversionException
  - Line Numbers: 25 (subscription check interval property)

#### 5.3.2 Invalid Block Timestamp Difference Setting
- **Description**: Handles invalid ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC value
- **Input**: ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC set to non-numeric value
- **Expected Result**: Service fails during event log DAO initialization, monitoring cannot start
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go (lines 49-51)
  - Functions: SubscribeAll() with strconv.Atoi()
  - Key Implementation: strconv.Atoi() conversion for allowableDiff, error returned on conversion failure
  - Line Numbers: 49-51 (timestamp diff conversion and error handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java (lines 81-87)
  - Functions: subscribeAll() with Integer.parseInt()
  - Key Implementation: Integer.parseInt() conversion, NumberFormatException handling, returns null on failure
  - Line Numbers: 82-87 (timestamp diff conversion and error handling)

## 6. Error Handling & Recovery
### 6.1 Normal Cases
#### 6.1.1 Retry Mechanism for WebSocket Errors
- **Description**: Automatically retries WebSocket connections on specific errors
- **Input**: WebSocket handshake errors ("rpc.wsHandshakeError")
- **Expected Result**: Service retries up to 5 times with 3-nanosecond delays (effectively instant), reinitializes monitor on retry
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/cmd/bc_monitoring/main.go (lines 10-16, 35-42)
  - Functions: retry.Do() with RetryIf condition
  - Key Implementation: rtyAttNum = uint(5), rtyDel = retry.Delay(3), specific error check for "rpc.wsHandshakeError"
  - Line Numbers: 10-16 (retry configuration), 37 (error condition), 38-41 (retry callback)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/RetryConfig.java (lines 20-34)
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/MonitoringRetryListener.java (lines 27-31)
  - Functions: retryTemplate(), MonitoringRetryListener.onError()
  - Key Implementation: SimpleRetryPolicy with 5 attempts, 3000ms backoff, WebSocketHandshakeException.class specific retry
  - Line Numbers: 23-24 (retry policy), 26-27 (backoff period), 29-30 (retry listener)

### 6.2 Semi-Normal Cases
#### 6.2.1 Graceful Degradation on Non-Critical Errors
- **Description**: Continues operation when non-critical errors occur
- **Input**: Events that cannot be parsed due to missing ABI definitions
- **Expected Result**: Specific events skipped, monitoring continues for other events
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go (lines 82-84, 146-152)
  - Functions: SubscribeAll(), convertEthLogToEventEntity() with ErrNoAbiEventFound handling
  - Key Implementation: errors.Is(err, ErrNoAbiEventFound) check, error not logged, processing continues
  - Line Numbers: 82-84 (error handling in subscription), 146-152 (error handling in conversion)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/EthEventLogDao.java (lines 192-195)
  - Functions: convBlock2EventEntities() with exception handling
  - Key Implementation: try-catch blocks around individual log processing, errors logged but processing continues
  - Line Numbers: 192-195 (individual log error handling)

#### 6.2.2 Internal Retry Pattern with goto RETRY
- **Description**: Service uses internal goto RETRY pattern for recoverable errors within monitoring loop
- **Input**: DynamoDB save failures, block number zero, empty transaction hash
- **Expected Result**: Service cancels current context, sleeps for checkInterval milliseconds, restarts monitoring from RETRY label (separate from main retry mechanism)
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 62-63, 72-74, 78-79, 90-92, 96-97, 104)
  - Functions: Execute() monitoring loop with goto RETRY pattern
  - Key Implementation: cancel(), time.Sleep(), goto RETRY pattern for recoverable errors
  - Line Numbers: 104 (RETRY label), multiple goto RETRY calls throughout monitoring loop
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java (lines 197-200, 158-163)
  - Functions: processNewTransactions(), processPendingTransactions() with method returns
  - Key Implementation: Method returns instead of goto pattern, Web3j shutdown and sleep handled at higher level
  - Line Numbers: 197-200 (new transaction error handling), 158-163 (pending transaction error handling)

### 6.3 Abnormal Cases
#### 6.3.1 Fatal Error Propagation
- **Description**: Properly propagates fatal errors that require service restart
- **Input**: Critical errors like DynamoDB connection failures, ABI parsing failures
- **Expected Result**: Errors propagated to main function, service exits with l.Fatalln()
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/cmd/bc_monitoring/main.go (lines 26-27, 30-31)
  - Functions: main() with l.Fatalln() calls
  - Key Implementation: Error propagation from Execute() methods, l.Fatalln(err) for fatal errors
  - Line Numbers: 26-27 (ABI download failure), 30-31 (monitor initialization failure)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/MonitoringRunnerConfig.java (lines 49-57)
  - Functions: commandLineRunner() with exception handling
  - Key Implementation: Exception propagation from execute() methods, logger.error() for fatal errors
  - Line Numbers: 49-51 (retry mechanism error), 55-57 (startup error)

#### 6.3.2 Non-Retryable Error Handling
- **Description**: Handles errors that should not trigger retry mechanism
- **Input**: Errors other than "rpc.wsHandshakeError"
- **Expected Result**: Service logs warning "restarting bc monitoring", continues infinite retry loop
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/cmd/bc_monitoring/main.go (lines 43-45)
  - Functions: main() infinite loop with error handling
  - Key Implementation: Infinite for loop, non-retryable errors logged as "restarting bc monitoring"
  - Line Numbers: 34 (infinite loop), 43-45 (non-retryable error handling)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/MonitoringRunnerConfig.java (lines 43-51)
  - Functions: commandLineRunner() with retryTemplate.execute()
  - Key Implementation: Spring Retry handles retryable vs non-retryable exceptions, non-retryable errors logged and propagated
  - Line Numbers: 43-48 (retry template execution), 49-51 (non-retryable error handling)

## 7. Logging & Monitoring
### 7.1 Normal Cases
#### 7.1.1 Structured Logging with Context
- **Description**: Logs include relevant context fields for debugging
- **Input**: Various service operations
- **Expected Result**: Logs include fields like tx_hash, block_height, event_name, trace_id
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 117-124)
  - Functions: saveTransaction(), savePendingTransaction() with logrus.Fields
  - Key Implementation: m.l.WithFields() for structured logging with context fields
  - Line Numbers: 117-124 (structured logging with event context)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/application/event/MonitorEventService.java (lines 229-236)
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/logging/StructuredLogContext.java (lines 72-87)
  - Functions: saveTransaction() with StructuredLogContext.forBlockchainEvent()
  - Key Implementation: try-with-resources StructuredLogContext for automatic MDC management
  - Line Numbers: 229-236 (structured logging with event context), 79-85 (context field mapping)

#### 7.1.2 Log Level Management
- **Description**: Appropriate log levels used for different scenarios
- **Input**: Various service events and errors
- **Expected Result**: Info for normal operations, Error for failures, Warn for retries
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 89, 113, 129)
  - File: dcbg-dcjpy-bcmonitoring/cmd/bc_monitoring/main.go (lines 39, 44)
  - Functions: Various logging calls with appropriate levels
  - Key Implementation: l.Warnln() for retries, l.Errorln() for failures, l.Infof() for success
  - Line Numbers: 89 (warn for block zero), 113 (error for empty hash), 129 (info for success)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/logging/LoggingService.java (lines 28-48)
  - Functions: configureLogLevel(), info(), warn(), error() methods
  - Key Implementation: Environment-based log level configuration, SLF4J with appropriate log levels
  - Line Numbers: 30-42 (log level configuration), 62-127 (level-specific methods)

### 7.2 Semi-Normal Cases
#### 7.2.1 Log Context Cleanup
- **Description**: Proper cleanup of log context between operations
- **Input**: Multiple events processed sequentially
- **Expected Result**: Log context doesn't leak between events, each event has correct context
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go (lines 117-124, 147-154)
  - Functions: saveTransaction(), savePendingTransaction() with scoped WithFields()
  - Key Implementation: Scoped logrus.WithFields() calls, context isolated per event
  - Line Numbers: 117-124 (transaction context), 147-154 (pending transaction context)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/logging/StructuredLogContext.java (lines 89-94)
  - Functions: StructuredLogContext.close() with try-with-resources
  - Key Implementation: AutoCloseable interface with MDC cleanup, automatic context isolation
  - Line Numbers: 89-94 (automatic MDC cleanup)

### 7.3 Abnormal Cases
#### 7.3.1 Logging System Failure
- **Description**: Service behavior when logging system fails
- **Input**: Logger initialization or write failures
- **Expected Result**: Service continues operation, may exit if logger cannot be initialized
- **Source Code Go**:
  - File: dcbg-dcjpy-bcmonitoring/cmd/bc_monitoring/main.go (lines 18-20)
  - Functions: init() with logger.NewLogger()
  - Key Implementation: Logger initialization in init(), service depends on successful logger creation
  - Line Numbers: 18-20 (logger initialization)
- **Source Code Java**:
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/logging/LoggingService.java (lines 28-48)
  - File: dcbg-dcjpy-bcmonitoring-java/src/main/resources/logback.xml (lines 52-57)
  - Functions: configureLogLevel() with @PostConstruct, Logback configuration
  - Key Implementation: Spring Boot manages logger lifecycle, Logback handles logging failures gracefully
  - Line Numbers: 28-48 (logger configuration), 52-57 (root logger setup)

package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.dynamodb

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDao
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import spock.lang.Specification

class EventDaoSpec extends Specification {

	DynamoDbClient mockDynamoDbClient
	BcmonitoringConfigurationProperties mockProperties
	BcmonitoringConfigurationProperties.Aws mockAws
	BcmonitoringConfigurationProperties.Aws.Dynamodb mockDynamodb
	LoggingService mockLogger
	EventDao eventDao

	def setup() {
		mockDynamoDbClient = Mock(DynamoDbClient)
		mockProperties = Mock(BcmonitoringConfigurationProperties)
		mockAws = Mock(BcmonitoringConfigurationProperties.Aws)
		mockDynamodb = Mock(BcmonitoringConfigurationProperties.Aws.Dynamodb)
		mockLogger = Mock(LoggingService)

		mockProperties.getAws() >> mockAws
		mockAws.getDynamodb() >> mockDynamodb
		mockDynamodb.getEventsTableName() >> "events"
		mockDynamodb.getTableNameWithPrefix("events") >> "prefix-events"

		eventDao = new EventDao(mockDynamoDbClient, mockProperties, mockLogger)
	}

	def "should successfully save an event"() {
		given:
		def event = Event.builder()
				.transactionHash("0x123456")
				.logIndex(1)
				.name("TestEvent")
				.indexedValues("indexed values")
				.nonIndexedValues("non-indexed values")
				.blockTimestamp(1234567890L)
				.log("log content")
				.build()

		when:
		def result = eventDao.save(event)

		then:
		1 * mockDynamoDbClient.putItem({ PutItemRequest request ->
			request.tableName() == "prefix-events" &&
					request.item() == event.toAttributeMap()
		})
		0 * mockLogger.error(_, _)
		result == true
	}

	def "should handle exception when saving event fails"() {
		given:
		def event = Event.builder()
				.transactionHash("0x123456")
				.logIndex(1)
				.name("TestEvent")
				.indexedValues("indexed values")
				.nonIndexedValues("non-indexed values")
				.blockTimestamp(1234567890L)
				.log("log content")
				.build()

		def exception = DynamoDbException.builder()
				.message("Test error message")
				.build()

		when:
		def result = eventDao.save(event)

		then:
		1 * mockDynamoDbClient.putItem(_ as PutItemRequest) >> { throw exception }
		1 * mockLogger.error("Failed to save event: " + event.transactionHash + ", logIndex: " + event.logIndex, exception)
		result == false
	}

	def "should throw NPE when handling null event"() {
		when:
		eventDao.save(null)

		then:
		def e = thrown(NullPointerException)
		e.message.contains("Cannot invoke")
	}

	def "should attempt to save event even with empty attribute map"() {
		given:
		def event = Mock(Event)
		event.toAttributeMap() >> [:]

		when:
		def result = eventDao.save(event)

		then:
		1 * mockDynamoDbClient.putItem({ PutItemRequest request ->
			request.tableName() == "prefix-events" &&
					request.item() == [:]
		})
		0 * mockLogger.error(_, _)
		result == true
	}
}

package com.decurret_dcp.dcjpy.bcmonitoring.exception;

/**
 * Base exception class for all custom exceptions in the bcmonitoring application. This provides a
 * consistent way to handle exceptions throughout the application.
 */
public class BcmonitoringException extends RuntimeException {

  private final String errorCode;

  /**
   * Constructs a new BcmonitoringException with the specified error code and detail message.
   *
   * @param errorCode the error code associated with this exception
   * @param message the detail message
   */
  public BcmonitoringException(String errorCode, String message) {
    super(message);
    this.errorCode = errorCode;
  }

  /**
   * Constructs a new BcmonitoringException with the specified error code, detail message, and
   * cause.
   *
   * @param errorCode the error code associated with this exception
   * @param message the detail message
   * @param cause the cause of this exception
   */
  public BcmonitoringException(String errorCode, String message, Throwable cause) {
    super(message, cause);
    this.errorCode = errorCode;
  }

  /**
   * Returns the error code associated with this exception.
   *
   * @return the error code
   */
  public String getErrorCode() {
    return errorCode;
  }
}

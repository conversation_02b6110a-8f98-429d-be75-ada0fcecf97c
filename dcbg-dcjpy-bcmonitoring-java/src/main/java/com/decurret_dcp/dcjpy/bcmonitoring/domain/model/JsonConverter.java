package com.decurret_dcp.dcjpy.bcmonitoring.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JsonConverter {
  private static final Logger log = LoggerFactory.getLogger(JsonConverter.class);
  private static final ObjectMapper OBJECT_MAPPER =
      new ObjectMapper()
          .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
          .setSerializationInclusion(JsonInclude.Include.NON_NULL)
          .enable(SerializationFeature.INDENT_OUTPUT);

  /**
   * Converts a JSON object to a string representation.
   *
   * @param jsonObject The JSON object to convert.
   * @return String representation of the JSON object
   * @throws RuntimeException if conversion fails
   */
  public static String toStringValue(Object jsonObject) {
    try {
      return OBJECT_MAPPER.writeValueAsString(jsonObject);
    } catch (JsonProcessingException e) {
      log.error("Error converting object to JSON string", e);
      throw new RuntimeException(e);
    }
  }
}

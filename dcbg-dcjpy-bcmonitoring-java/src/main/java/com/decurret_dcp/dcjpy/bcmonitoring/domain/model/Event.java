package com.decurret_dcp.dcjpy.bcmonitoring.domain.model;

import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import org.web3j.protocol.core.methods.response.EthBlock;
import org.web3j.protocol.core.methods.response.Log;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

@Builder
public class Event {
  public final String transactionHash;
  public final int logIndex;
  public final String name;
  public final String indexedValues;
  public final String nonIndexedValues;
  public final long blockTimestamp;
  public final String log;

  /**
   * Converts a Map<String, AttributeValue> to an Event object.
   *
   * @param attributeValueMap The map containing the event data.
   * @return An Event object.
   */
  public static Event of(Map<String, AttributeValue> attributeValueMap) {
    return Event.builder()
        .transactionHash(attributeValueMap.get("transactionHash").s())
        .logIndex(Integer.valueOf(attributeValueMap.get("logIndex").n()))
        .name(attributeValueMap.get("name").s())
        .indexedValues(attributeValueMap.get("indexedValues").s())
        .nonIndexedValues(attributeValueMap.get("nonIndexedValues").s())
        .blockTimestamp(Long.valueOf(attributeValueMap.get("blockTimestamp").n()))
        .log(attributeValueMap.get("log").s())
        .build();
  }

  /**
   * Converts the Event object to a Map<String, AttributeValue>.
   *
   * @return A map containing the event data.
   */
  public Map<String, AttributeValue> toAttributeMap() {
    Map<String, AttributeValue> item = new HashMap<>();
    item.put("transactionHash", AttributeValue.builder().s(transactionHash).build());
    item.put("logIndex", AttributeValue.builder().n(String.valueOf(logIndex)).build());
    item.put("name", AttributeValue.builder().s(name).build());
    item.put("indexedValues", AttributeValue.builder().s(indexedValues).build());
    item.put("nonIndexedValues", AttributeValue.builder().s(nonIndexedValues).build());
    item.put("blockTimestamp", AttributeValue.builder().n(String.valueOf(blockTimestamp)).build());
    item.put("log", AttributeValue.builder().s(log).build());
    return item;
  }

  /**
   * Builds an Event object from a Log and a Block.
   *
   * @param log The log object.
   * @param block The block object.
   * @return An Event object.
   */
  public static Event buildEventWithLogAndBlock(Log log, EthBlock.Block block) {
    return Event.builder()
        .transactionHash(log.getTransactionHash())
        .logIndex(log.getLogIndex().intValue())
        .name(log.getTopics().getFirst())
        .indexedValues(log.getTopics().toString())
        .nonIndexedValues(log.getData())
        .blockTimestamp(block.getTimestamp().longValue())
        .log(log.toString())
        .build();
  }
}

package com.decurret_dcp.dcjpy.bcmonitoring.config;

import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/** Configuration class for logging. This class configures the logging system. */
@Configuration
public class LoggingConfig {

  /**
   * Creates a new LoggingService.
   *
   * @param configProperties the configuration properties
   * @return a new LoggingService
   */
  @Bean
  @Primary
  public LoggingService loggingService(BcmonitoringConfigurationProperties configProperties) {
    return new LoggingService(configProperties);
  }

  /**
   * Creates a new LoggingService for backward compatibility.
   *
   * @param configProperties the configuration properties
   * @return a new LoggingService
   */
  @Bean(name = "legacyLoggingService")
  public LoggingService legacyLoggingService(BcmonitoringConfigurationProperties configProperties) {
    return new LoggingService(configProperties);
  }
}

package com.decurret_dcp.dcjpy.bcmonitoring.config;

import static com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst.LOCAL;
import static com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst.PROD;

import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;

@Configuration
public class AwsCredentialsConfig {

  private final BcmonitoringConfigurationProperties configProperties;
  private final LoggingService logger;

  public AwsCredentialsConfig(
      BcmonitoringConfigurationProperties configProperties, LoggingService logger) {
    this.configProperties = configProperties;
    this.logger = logger;
  }

  /**
   * Configures the AWS credentials provider based on the environment settings.
   *
   * <p>For local development, it uses LocalStack credentials. For production, it uses the default
   * credentials provider. For other environments, it uses the configured AWS credentials.
   *
   * @return AwsCredentialsProvider
   */
  @Bean
  public AwsCredentialsProvider awsCredentialsProvider() {
    if (LOCAL.equals(configProperties.getEnv())) {
      logger.info("Using LocalStack credentials for local environment");
      return StaticCredentialsProvider.create(
          AwsBasicCredentials.create(
              configProperties.getLocalstack().getAccessKey(),
              configProperties.getLocalstack().getSecretKey()));
    } else if (PROD.equals(configProperties.getEnv())) {
      logger.info("Using default credentials provider for production environment");
      return DefaultCredentialsProvider.create();
    } else {
      // For other environments like dev, staging, etc.
      logger.info("Using configured AWS credentials for environment: " + configProperties.getEnv());
      return StaticCredentialsProvider.create(
          AwsBasicCredentials.create(
              configProperties.getAws().getAccessKeyId(),
              configProperties.getAws().getSecretAccessKey()));
    }
  }
}

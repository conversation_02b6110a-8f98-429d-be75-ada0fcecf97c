package com.decurret_dcp.dcjpy.bcmonitoring.config;

import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;
import java.net.URI;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

@Configuration
public class DynamoDBConfig {
  private final BcmonitoringConfigurationProperties configuration;
  private final AwsCredentialsProvider credentialsProvider;

  public DynamoDBConfig(
      BcmonitoringConfigurationProperties configuration,
      AwsCredentialsProvider credentialsProvider) {
    this.configuration = configuration;
    this.credentialsProvider = credentialsProvider;
  }

  @Bean
  public DynamoDbClient dynamoDbClient() {
    if (DCFConst.LOCAL.equals(configuration.getEnv())) {
      return DynamoDbClient.builder()
          .endpointOverride(URI.create(configuration.getLocalstack().getEndpoint()))
          .region(Region.of(configuration.getLocalstack().getRegion()))
          .credentialsProvider(credentialsProvider)
          .build();
    } else {
      return DynamoDbClient.builder()
          .region(Region.of(configuration.getAws().getRegion()))
          .credentialsProvider(DefaultCredentialsProvider.create())
          .credentialsProvider(credentialsProvider)
          .build();
    }
  }
}

package com.decurret_dcp.dcjpy.bcmonitoring.exception;

/** Exception thrown when there is an error accessing data from a data store. */
public class DataAccessException extends BcmonitoringException {

  private static final String ERROR_CODE = "DATA_ACCESS_ERROR";

  /**
   * Constructs a new DataAccessException with the specified detail message.
   *
   * @param message the detail message
   */
  public DataAccessException(String message) {
    super(ERROR_CODE, message);
  }

  /**
   * Constructs a new DataAccessException with the specified detail message and cause.
   *
   * @param message the detail message
   * @param cause the cause of this exception
   */
  public DataAccessException(String message, Throwable cause) {
    super(ERROR_CODE, message, cause);
  }
}

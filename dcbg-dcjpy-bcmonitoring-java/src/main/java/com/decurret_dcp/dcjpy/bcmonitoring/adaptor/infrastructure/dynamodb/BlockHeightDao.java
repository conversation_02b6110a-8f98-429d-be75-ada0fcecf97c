package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.DataAccessException;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;
import software.amazon.awssdk.services.dynamodb.model.PutItemRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryResponse;

@Component
public class BlockHeightDao implements BlockHeightRepository {
  private final DynamoDbClient dynamoDbClient;
  private final BcmonitoringConfigurationProperties properties;
  private final LoggingService logger;

  public BlockHeightDao(
      DynamoDbClient dynamoDbClient,
      BcmonitoringConfigurationProperties properties,
      LoggingService logger) {
    this.dynamoDbClient = dynamoDbClient;
    this.logger = logger;
    this.properties = properties;
  }

  /**
   * Retrieves a block height from the DynamoDB table.
   *
   * @return The block height number.
   * @throws DataAccessException if there was an error during the operation.
   */
  public long get() {
    try {
      BcmonitoringConfigurationProperties.Aws.Dynamodb dynamodb = properties.getAws().getDynamodb();

      // Create expression attribute values
      Map<String, AttributeValue> expressionValues = new HashMap<>();
      expressionValues.put(":id", AttributeValue.builder().n("1").build());

      // Create query request
      QueryRequest queryRequest =
          QueryRequest.builder()
              .tableName(dynamodb.getTableNameWithPrefix(dynamodb.getBlockHeightTableName()))
              .keyConditionExpression("id = :id")
              .expressionAttributeValues(expressionValues)
              .build();

      // Execute query
      QueryResponse response = dynamoDbClient.query(queryRequest);

      List<BlockHeight> blockHeights = buildBlockHeightsFromMapValue(response);

      // Check if the response is empty
      if (blockHeights.isEmpty()) {
        return 0L; // Return 0 when no block heights found
      }
      return blockHeights.getFirst().blockNumber; // Return the first block height's number

    } catch (DynamoDbException e) {
      String errorMessage = "Error retrieving block height from DynamoDB";
      logger.error(errorMessage, e);
      throw new DataAccessException(errorMessage, e);
    }
  }

  /**
   * * Builds a list of BlockHeight objects from a map of attribute values.
   *
   * @param response The response containing the block height data.
   * @return A list of BlockHeight objects.
   */
  @NotNull private static List<BlockHeight> buildBlockHeightsFromMapValue(QueryResponse response) {
    List<BlockHeight> blockHeights = new ArrayList<>();
    for (Map<String, AttributeValue> item : response.items()) {
      BlockHeight blockHeight =
          BlockHeight.builder()
              .id(Long.parseLong(item.get("id").n()))
              .blockNumber(Long.parseLong(item.get("blockNumber").n()))
              .build();
      blockHeights.add(blockHeight);
    }
    return blockHeights;
  }

  /**
   * Saves a block height to the DynamoDB table.
   *
   * @param blockHeight The block height to save.
   * @return true if the operation was successful, false otherwise.
   */
  @Override
  public boolean save(BlockHeight blockHeight) {
    BcmonitoringConfigurationProperties.Aws.Dynamodb dynamodb = properties.getAws().getDynamodb();

    try {
      dynamoDbClient.putItem(
          PutItemRequest.builder()
              .tableName(dynamodb.getTableNameWithPrefix(dynamodb.getBlockHeightTableName()))
              .item(blockHeight.toAttributeMap())
              .build());
      return true;
    } catch (DynamoDbException dbException) {
      String errorMessage = "Failed to save block height: " + blockHeight.blockNumber;
      logger.error(errorMessage, dbException);
      // We're returning false instead of throwing an exception to maintain backward compatibility
      // with existing code that expects a boolean return value
      return false;
    }
  }
}

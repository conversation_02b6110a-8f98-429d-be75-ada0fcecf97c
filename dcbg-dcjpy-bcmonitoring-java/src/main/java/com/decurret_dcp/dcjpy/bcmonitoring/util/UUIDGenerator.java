package com.decurret_dcp.dcjpy.bcmonitoring.util;

import com.github.f4b6a3.uuid.UuidCreator;
import java.util.UUID;

/**
 * UUIDGenerator is a utility class for generating UUIDs. It uses the UuidCreator library to create
 * random UUIDs.
 */
public class UUIDGenerator {
  private final UUID bytes;

  private UUIDGenerator(UUID bytes) {
    this.bytes = bytes;
  }

  /**
   * Generates a new random UUID.
   *
   * @return a new UUIDGenerator instance with a random UUID
   */
  public static UUIDGenerator New() {
    UUID uuid = UuidCreator.getRandomBased();
    return new UUIDGenerator(uuid);
  }

  public UUID getUUID() {
    return bytes;
  }

  @Override
  public String toString() {
    return bytes.toString();
  }
}

package com.decurret_dcp.dcjpy.bcmonitoring.exception;

/** Exception thrown when there is an error in the application configuration. */
public class ConfigurationException extends BcmonitoringException {

  private static final String ERROR_CODE = "CONFIGURATION_ERROR";

  /**
   * Constructs a new ConfigurationException with the specified detail message.
   *
   * @param message the detail message
   */
  public ConfigurationException(String message) {
    super(ERROR_CODE, message);
  }

  /**
   * Constructs a new ConfigurationException with the specified detail message and cause.
   *
   * @param message the detail message
   * @param cause the cause of this exception
   */
  public ConfigurationException(String message, Throwable cause) {
    super(ERROR_CODE, message, cause);
  }
}

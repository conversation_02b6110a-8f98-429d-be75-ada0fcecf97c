package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.sqs;

import java.util.List;

public class EventLogRepository {

  // ToDo: Implement the method to get logs from SQS
  List<Object> getFilterLogs(
      String contractAddress, String eventSignature, long fromBlock, long toBlock) {
    return List.of();
  }

  // ToDo: Implement the method to get logs from SQS
  void subscribe(String contractAddress, String eventSignature, long fromBlock) {}
}

package com.decurret_dcp.dcjpy.bcmonitoring.util;

import com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.TraceIdExtractorService;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BlockchainDataMapper {

  private final LoggingService logger;
  private final ObjectMapper objectMapper;
  private final TraceIdExtractorService traceIdExtractor;

  @Autowired
  public BlockchainDataMapper(
      LoggingService logger, ObjectMapper objectMapper, TraceIdExtractorService traceIdExtractor) {
    this.logger = logger;
    this.objectMapper = objectMapper;
    this.traceIdExtractor = traceIdExtractor;
  }

  public <T> T fromJson(String json, Class<T> clazz) {
    try {
      return objectMapper.readValue(json, clazz);
    } catch (Exception e) {
      logger.error("Failed to parse JSON", e);
      return null;
    }
  }

  public String toJson(Object object) {
    try {
      return objectMapper.writeValueAsString(object);
    } catch (Exception e) {
      logger.error("Failed to convert to JSON", e);
      return "{}";
    }
  }

  public String extractTraceId(String nonIndexedValues) {
    return traceIdExtractor.fetchTraceId(nonIndexedValues);
  }

  public Map<String, Object> jsonToMap(String json) {
    try {
      return objectMapper.readValue(json, Map.class);
    } catch (Exception e) {
      logger.error("Failed to convert JSON to map", e);
      return Map.of();
    }
  }
}

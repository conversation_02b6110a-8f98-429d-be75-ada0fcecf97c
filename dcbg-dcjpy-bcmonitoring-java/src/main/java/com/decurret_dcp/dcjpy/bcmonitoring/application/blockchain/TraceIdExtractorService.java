package com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain;

import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ParsedTraceId;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TraceIdExtractorService {

  private final LoggingService logger;
  private final ObjectMapper objectMapper;

  @Autowired
  public TraceIdExtractorService(LoggingService logger, ObjectMapper objectMapper) {
    this.logger = logger;
    this.objectMapper = objectMapper;
  }

  /**
   * * Extracts the trace ID from the given non-indexed values JSON string.
   *
   * @param nonIndexedValues JSON string containing the trace ID
   * @return Extracted trace ID as a string
   * @throws RuntimeException if parsing fails
   */
  public String fetchTraceId(String nonIndexedValues) {
    try {
      ParsedTraceId parsed = objectMapper.readValue(nonIndexedValues, ParsedTraceId.class);

      if (parsed.getTraceId() == null || parsed.getTraceId().length == 0) {
        return "";
      }

      // Filter out zero bytes
      ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
      for (byte b : parsed.getTraceId()) {
        if (b != 0) {
          outputStream.write(b);
        }
      }

      return outputStream.toString(StandardCharsets.UTF_8);
    } catch (JsonProcessingException e) {
      logger.error("Error parsing non-indexed values", e);
      return "";
    }
  }
}

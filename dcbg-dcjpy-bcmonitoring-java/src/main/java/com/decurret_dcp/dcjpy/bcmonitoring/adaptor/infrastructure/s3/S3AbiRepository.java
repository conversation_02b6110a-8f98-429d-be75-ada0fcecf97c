package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import java.io.InputStream;
import java.util.List;
import software.amazon.awssdk.services.s3.model.CommonPrefix;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;

public interface S3AbiRepository {
  /**
   * List common prefixes of objects in a bucket.
   *
   * @param bucketName The name of the S3 bucket.
   * @param delimiter The delimiter to use for grouping objects.
   * @return A list of common prefixes.
   */
  List<CommonPrefix> listCommonPrefixesObjects(String bucketName, String delimiter);

  /**
   * List objects in a bucket with a specific prefix.
   *
   * @param bucketName The name of the S3 bucket.
   * @param prefix The prefix to filter objects.
   * @return A ListObjectsV2Response containing the objects.
   */
  ListObjectsV2Response listObjects(String bucketName, String prefix);

  /**
   * Get an object from S3.
   *
   * @param bucketName The name of the S3 bucket.
   * @param key The key of the object to retrieve.
   * @return An InputStream for the object.
   */
  InputStream getObject(String bucketName, String key);
}

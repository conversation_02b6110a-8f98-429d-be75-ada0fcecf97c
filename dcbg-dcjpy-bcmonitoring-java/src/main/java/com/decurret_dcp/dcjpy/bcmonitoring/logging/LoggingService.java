package com.decurret_dcp.dcjpy.bcmonitoring.logging;

import static com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst.LOCAL;
import static com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst.PROD;
import static com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst.TEST;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

@Component
public class LoggingService {
  // Manually define Logger using LoggerFactory
  private static final Logger logger = LoggerFactory.getLogger(LoggingService.class);
  private final BcmonitoringConfigurationProperties configProperties;

  public LoggingService(BcmonitoringConfigurationProperties configProperties) {
    this.configProperties = configProperties;
  }

  // Set the log level based on the environment
  @PostConstruct
  private void configureLogLevel() {
    switch (configProperties.getEnv().toLowerCase()) {
      case PROD:
        System.setProperty("logging.level.root", "INFO");
        break;
      case LOCAL:
        System.setProperty("logging.level.root", "DEBUG");
        break;
      case TEST:
        System.setProperty("logging.level.root", "ERROR");
        break;
      default:
        System.setProperty("logging.level.root", "TRACE");
        break;
    }
    logger.info(
        "Log level set to {} based on environment: {}",
        System.getProperty("logging.level.root"),
        configProperties.getEnv());
  }

  /** Logs a message at DEBUG level */
  public void debug(String message) {
    logger.debug(message);
  }

  /** Logs a message at DEBUG level with context data */
  public void debug(String message, Map<String, Object> contextData) {
    try (var logContext = StructuredLogContext.builder().putAll(contextData).build()) {
      logger.debug(message);
    }
  }

  /** Logs a message at INFO level */
  public void info(String message) {
    logger.info(message);
  }

  /** Logs a message at INFO level with context data */
  public void info(String message, Map<String, Object> contextData) {
    try (var logContext = StructuredLogContext.builder().putAll(contextData).build()) {
      logger.info(message);
    }
  }

  /** Logs a formatted message at INFO level with parameters */
  public void info(String format, Object... args) {
    logger.info(format, args);
  }

  /** Logs a message at WARN level */
  public void warn(String message) {
    logger.warn(message);
  }

  /** Logs a formatted message at WARN level with parameters */
  public void warn(String format, Object... args) {
    logger.warn(format, args);
  }

  /** Logs a message at WARN level with context data */
  public void warn(String message, Map<String, Object> contextData) {
    try (var logContext = StructuredLogContext.builder().putAll(contextData).build()) {
      logger.warn(message);
    }
  }

  /** Logs a message at ERROR level */
  public void error(String message) {
    logger.error(message);
  }

  /** Logs a message at ERROR level with context data */
  public void error(String message, Map<String, Object> contextData) {
    try (var logContext = StructuredLogContext.builder().putAll(contextData).build()) {
      logger.error(message);
    }
  }

  /** Logs an error with exception */
  public void error(String message, Throwable throwable) {
    logger.error(message, throwable);
  }

  /** Logs an error with exception and context data */
  public void error(String message, Throwable throwable, Map<String, Object> contextData) {
    Map<String, Object> contextWithException = new HashMap<>(contextData);
    contextWithException.put("exception_class", throwable.getClass().getName());
    contextWithException.put("exception_message", throwable.getMessage());

    try (var logContext = StructuredLogContext.builder().putAll(contextWithException).build()) {
      logger.error(message, throwable);
    }
  }

  /** Logs a formatted message at error level with parameters */
  public void error(String format, Object... args) {
    logger.error(format, args);
  }

  /** Generate and set a correlation ID */
  public static String initializeCorrelationId() {
    String correlationId = UUID.randomUUID().toString();
    MDC.put("correlationId", correlationId);
    return correlationId;
  }

  /** Clear correlation ID */
  public static void clearCorrelationId() {
    MDC.remove("correlationId");
  }

  /**
   * Creates a structured log context for a blockchain event.
   *
   * @param eventName the event name
   * @param txHash the transaction hash
   * @param blockHeight the block height
   * @param logIndex the log index
   * @param blockTimestamp the block timestamp
   * @param traceId the trace ID
   * @return a new StructuredLogContext
   */
  public static StructuredLogContext forBlockchainEvent(
      String eventName,
      String txHash,
      long blockHeight,
      int logIndex,
      long blockTimestamp,
      String traceId) {
    return StructuredLogContext.forBlockchainEvent(
        eventName, txHash, blockHeight, logIndex, blockTimestamp, traceId);
  }
}

package com.decurret_dcp.dcjpy.bcmonitoring.exception;

/** Exception thrown when a requested resource is not found. */
public class ResourceNotFoundException extends BcmonitoringException {

  private static final String ERROR_CODE = "RESOURCE_NOT_FOUND";

  /**
   * Constructs a new ResourceNotFoundException with the specified detail message.
   *
   * @param message the detail message
   */
  public ResourceNotFoundException(String message) {
    super(ERROR_CODE, message);
  }

  /**
   * Constructs a new ResourceNotFoundException with the specified detail message and cause.
   *
   * @param message the detail message
   * @param cause the cause of this exception
   */
  public ResourceNotFoundException(String message, Throwable cause) {
    super(ERROR_CODE, message, cause);
  }
}

package com.decurret_dcp.dcjpy.bcmonitoring.exception;

/** Exception thrown when there is an error interacting with S3. */
public class S3Exception extends BcmonitoringException {

  private static final String ERROR_CODE = "S3_ERROR";

  /**
   * Constructs a new S3Exception with the specified detail message.
   *
   * @param message the detail message
   */
  public S3Exception(String message) {
    super(ERROR_CODE, message);
  }

  /**
   * Constructs a new S3Exception with the specified detail message and cause.
   *
   * @param message the detail message
   * @param cause the cause of this exception
   */
  public S3Exception(String message, Throwable cause) {
    super(ERROR_CODE, message, cause);
  }
}

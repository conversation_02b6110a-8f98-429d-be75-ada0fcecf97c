package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.BlockchainException;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import java.util.concurrent.BlockingQueue;
import org.springframework.stereotype.Repository;

/** Repository implementation for Ethereum event logs */
@Repository
public class EventLogRepositoryImpl implements EventLogRepository {
  private final LoggingService logger;
  private final EthEventLogDao eventLogDao;

  public EventLogRepositoryImpl(LoggingService logger, EthEventLogDao eventLogDao) {
    this.logger = logger;
    this.eventLogDao = eventLogDao;
  }

  /**
   * Subscribe to blockchain events
   *
   * @return Queue of transactions containing events
   * @throws BlockchainException if there is an error subscribing to events
   */
  @Override
  public BlockingQueue<Transaction> subscribe() {
    try {
      return eventLogDao.subscribeAll();
    } catch (Exception e) {
      String errorMessage = "Error subscribing to blockchain events";
      logger.error(errorMessage, e);
      throw new BlockchainException(errorMessage, e);
    }
  }

  /**
   * Get filtered logs from a specific block height
   *
   * @param blockHeight Block number to start from
   * @return Queue of transactions containing events
   * @throws BlockchainException if there is an error getting filter logs
   */
  @Override
  public BlockingQueue<Transaction> getFilterLogs(long blockHeight) {
    try {
      return eventLogDao.getPendingTransactions(blockHeight);
    } catch (Exception e) {
      String errorMessage = "Error getting filter logs from block height: " + blockHeight;
      logger.error(errorMessage, e);
      throw new BlockchainException(errorMessage, e);
    }
  }
}

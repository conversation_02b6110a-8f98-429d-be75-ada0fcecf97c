package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.stereotype.Component;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Bool;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.abi.datatypes.Event;
import org.web3j.abi.datatypes.generated.Uint112;
import org.web3j.abi.datatypes.generated.Uint256;

@Component
public class EventSignatureRegistry {
  private final LoggingService log;
  private final Map<String, String> eventSignatures = new ConcurrentHashMap<>();

  public EventSignatureRegistry(LoggingService log) {
    this.log = log;
  }

  /**
   * Register an event with its name and signature
   *
   * @param event The event to register
   * @param eventName The name of the event
   */
  public void registerEvent(Event event, String eventName) {
    String signature = EventEncoder.encode(event);
    eventSignatures.put(signature, eventName);
    log.info("Registered event: {} with signature: {}", eventName, signature);
  }

  /**
   * Register common events
   *
   * <p>This method registers common Ethereum events such as ERC-20, ERC-721, Uniswap, and ERC-1155
   * events.
   */
  @PostConstruct
  public void registerCommonEvents() {
    // ERC-20 Events
    registerEvent(
        new Event(
            "Transfer",
            Arrays.asList(
                new TypeReference<Address>(true) {},
                new TypeReference<Address>(true) {},
                new TypeReference<Uint256>(false) {})),
        "Transfer");

    registerEvent(
        new Event(
            "Approval",
            Arrays.asList(
                new TypeReference<Address>(true) {},
                new TypeReference<Address>(true) {},
                new TypeReference<Uint256>(false) {})),
        "Approval");

    // ERC-721 Events
    registerEvent(
        new Event(
            "Transfer",
            Arrays.asList(
                new TypeReference<Address>(true) {},
                new TypeReference<Address>(true) {},
                new TypeReference<Uint256>(true) {})),
        "ERC721Transfer");

    registerEvent(
        new Event(
            "ApprovalForAll",
            Arrays.asList(
                new TypeReference<Address>(true) {},
                new TypeReference<Address>(true) {},
                new TypeReference<Bool>(false) {})),
        "ApprovalForAll");

    // Uniswap Events
    registerEvent(
        new Event(
            "Swap",
            Arrays.asList(
                new TypeReference<Address>(true) {},
                new TypeReference<Uint256>(false) {},
                new TypeReference<Uint256>(false) {},
                new TypeReference<Uint256>(false) {},
                new TypeReference<Uint256>(false) {})),
        "Swap");

    registerEvent(
        new Event(
            "Sync",
            Arrays.asList(
                new TypeReference<Uint112>(false) {}, new TypeReference<Uint112>(false) {})),
        "Sync");

    // ERC-1155 Events
    registerEvent(
        new Event(
            "TransferSingle",
            Arrays.asList(
                new TypeReference<Address>(true) {},
                new TypeReference<Address>(true) {},
                new TypeReference<Address>(true) {},
                new TypeReference<Uint256>(false) {},
                new TypeReference<Uint256>(false) {})),
        "TransferSingle");

    registerEvent(
        new Event(
            "TransferBatch",
            Arrays.asList(
                new TypeReference<Address>(true) {},
                new TypeReference<Address>(true) {},
                new TypeReference<Address>(true) {},
                new TypeReference<DynamicArray<Uint256>>(false) {},
                new TypeReference<DynamicArray<Uint256>>(false) {})),
        "TransferBatch");
  }

  /**
   * Resolve the event name from the event signature
   *
   * @param signature The event signature
   * @return The event name or "UnknownEvent" if not found
   */
  public String resolveEventName(String signature) {
    return eventSignatures.getOrDefault(signature, "UnknownEvent");
  }
}

# dcbg-dcjpy-bcmonitoring-java

BCモニタリングは、Ethereumクライアントで発生したイベントを取得し、そのデータをデータベースに保存する役割を持つアプリケーションである。

## 構成

[こちら](https://decurret.atlassian.net/wiki/spaces/DIG/pages/3612312322/BCMonitoring+JAVA)を参照ください。

## ライブラリ情報

[こちら](https://decurret.atlassian.net/wiki/spaces/DIG/pages/3610771493/BCMonitoring+JAVA?atlOrigin=eyJpIjoiMmZkOTNhYjQzMzZjNGFhNjg0ZDllNWVjZDhkOThkOTUiLCJwIjoiYyJ9)を参照ください。

ライブラリの追加/変更作業時にはドキュメントの更新が必要である。

## 環境変数一覧

[環境変数一覧(BCMonitoring Web3Stream RDSStream](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2893185483/_24.07+_+BCMonitoring+BCMonitoring+Stream#BCMonitoring-Stream)


## ローカルでの起動方法

TODO:
[こちら]を参照ください。

#### Dockerを使用した起動方法

```bash
# イメージをビルド
docker build -t dcbg-dcjpy-bcmonitoring:latest .

# コンテナを起動
docker-compose up -d
```

#### Gradle Localを使用した起動方法
```bash
# 静的分析を行い、コードフォーマットやスタイルを修正する。
./gradlew spotlessGroovyGradleApply

# プロジェクトを構築する。
./gradlew build

```

## ローカルでのテスト

```shell
./gradlew build
```

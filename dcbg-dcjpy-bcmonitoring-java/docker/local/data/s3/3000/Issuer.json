{"address": "0x721bb253A7779cE67Af8b6527B988Fa4d47EbE27", "abi": [{"type": "event", "anonymous": false, "name": "AddAccountBy<PERSON><PERSON>uer", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "<PERSON>d<PERSON><PERSON><PERSON>", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "uint16", "name": "bankCode", "indexed": true}, {"type": "string", "name": "name", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddIssuerRole", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "address", "name": "issuerE<PERSON>", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "CumulativeReset", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}, {"type": "bytes32", "name": "validatorId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "IssuerEnabled", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "string", "name": "name", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ModTokenLimit", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": true}, {"type": "bool[]", "name": "itemFlgs"}, {"type": "uint256[]", "name": "limitAmounts"}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "ROLE_PREFIX_ISSUER", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "addAccountId", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "addAccountRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "address", "name": "accountEoa"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "add<PERSON><PERSON><PERSON>", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "uint16", "name": "bankCode"}, {"type": "string", "name": "name"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addIssuerRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "address", "name": "issuerE<PERSON>"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "checkBurn", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkMint", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkRole", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "hash"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "has"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "cumulativeReset", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "forceBurn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "getAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32[]", "name": "inAccountIds"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "offset"}], "outputs": [{"type": "tuple[]", "name": "accounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "reasonCode"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "get<PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}], "outputs": [{"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getIssuerAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "tuple", "name": "issuer", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}, {"type": "bool", "name": "issuerIdExistence"}, {"type": "address", "name": "issuerE<PERSON>"}, {"type": "tuple[]", "name": "issuerAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByIssuerId"}]}]}]}, {"type": "function", "name": "getIssuerCount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": "count"}]}, {"type": "function", "name": "getIssuerId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getIssuerList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "offset"}], "outputs": [{"type": "tuple[]", "name": "issuers", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "hasAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "isFrozen", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "frozen"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "mod<PERSON><PERSON><PERSON>", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "string", "name": "name"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "modTokenLimit", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bool[]", "name": "itemFlgs"}, {"type": "uint256[]", "name": "limitAmounts"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setAccountStatus", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setIssuerAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "issuer", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}, {"type": "bool", "name": "issuerIdExistence"}, {"type": "address", "name": "issuerE<PERSON>"}, {"type": "tuple[]", "name": "issuerAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByIssuerId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x8cb886198297153cd535b7c521f44d849b26d7d2f22d0b93c475ce6b551726d7", "receipt": {"to": null, "from": "0x027CaDD533C40e8017de7ac814baE35f8b08f402", "contractAddress": "0x721bb253A7779cE67Af8b6527B988Fa4d47EbE27", "transactionIndex": 0, "gasUsed": "6115397", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xfd7f5683809adf4be56b85a11e02e530f17b2bfc01b5c469f8f5fd908c2fd4df", "blockNumber": 321, "cumulativeGasUsed": "6115397", "status": 1}}
{"address": "0x4Cd8015e0Fdb9f21de8337EFDf7ee95fb1C50054", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "accessCtrl", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "account", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "balanceSyncBridge", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "businessZoneAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "financialCheck", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "financialZoneAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "ibcApp", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "string", "name": "ibcAppName"}], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "ibcToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [], "outputs": []}, {"type": "function", "name": "issuer", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "provider", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "setContracts", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "contracts", "components": [{"type": "address", "name": "ctrlAddress"}, {"type": "address", "name": "providerAddress"}, {"type": "address", "name": "issuer<PERSON><PERSON><PERSON>"}, {"type": "address", "name": "validator<PERSON><PERSON><PERSON>"}, {"type": "address", "name": "accountAddress"}, {"type": "address", "name": "financialZoneAccountAddress"}, {"type": "address", "name": "businessZoneAccountAddress"}, {"type": "address", "name": "tokenAddress"}, {"type": "address", "name": "ibc<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "address", "name": "financialCheckAddress"}, {"type": "address", "name": "transferProxyAddress"}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setIbcApp", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "ibcAppAddress"}, {"type": "string", "name": "ibcAppName"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "token", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "transferProxy", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "validator", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x060f218d90568043def9bb79d37893b7a9545cacdfbc1284f27c20fef0cb4904", "receipt": {"to": null, "from": "0x027CaDD533C40e8017de7ac814baE35f8b08f402", "contractAddress": "0x4Cd8015e0Fdb9f21de8337EFDf7ee95fb1C50054", "transactionIndex": 0, "gasUsed": "1388509", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x8ec097e66733b4f82756b844ffdff59601f854fd50f6243f025e9ff5a55716fd", "blockNumber": 287, "cumulativeGasUsed": "1388509", "status": 1}}
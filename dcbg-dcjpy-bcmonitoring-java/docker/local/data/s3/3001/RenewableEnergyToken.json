{"address": "0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512", "abi": [{"type": "event", "anonymous": false, "name": "CustomTransfer", "inputs": [{"type": "bytes32", "name": "sendAccountId", "indexed": false}, {"type": "bytes32", "name": "fromAccountId", "indexed": false}, {"type": "bytes32", "name": "toAccountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "bytes32", "name": "miscValue1", "indexed": false}, {"type": "string", "name": "miscValue2", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "MintRNToken", "inputs": [{"type": "bytes32", "name": "tokenId", "indexed": true}, {"type": "bytes32", "name": "metadataId", "indexed": false}, {"type": "bytes32", "name": "metadataHash", "indexed": false}, {"type": "bytes32", "name": "mintAccountId", "indexed": false}, {"type": "bytes32", "name": "ownerAccountId", "indexed": false}, {"type": "bool", "name": "isLocked", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "TransferRNToken", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "sendAccountId", "indexed": false}, {"type": "bytes32", "name": "fromAccountId", "indexed": false}, {"type": "bytes32", "name": "toAccountId", "indexed": false}, {"type": "bytes32", "name": "tokenId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "backupRenewableEnergyTokens", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "renewableEnergyTokenAll", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "tuple", "name": "renewableEnergyTokenData", "components": [{"type": "uint8", "name": "tokenStatus"}, {"type": "bytes32", "name": "metadataId"}, {"type": "bytes32", "name": "metadataHash"}, {"type": "bytes32", "name": "mintAccountId"}, {"type": "bytes32", "name": "ownerAccountId"}, {"type": "bytes32", "name": "previousAccountId"}, {"type": "bool", "name": "isLocked"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupTokenIdsByAccountIds", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "tokenIdsByAccountIdAll", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32[]", "name": "tokenIds"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkTransaction", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "customTransfer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}, {"type": "bytes32", "name": "traceId"}], "outputs": [{"type": "bool", "name": "result"}]}, {"type": "function", "name": "getRenewableEnergyTokenAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "tuple", "name": "renewableEnergyToken", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "tuple", "name": "renewableEnergyTokenData", "components": [{"type": "uint8", "name": "tokenStatus"}, {"type": "bytes32", "name": "metadataId"}, {"type": "bytes32", "name": "metadataHash"}, {"type": "bytes32", "name": "mintAccountId"}, {"type": "bytes32", "name": "ownerAccountId"}, {"type": "bytes32", "name": "previousAccountId"}, {"type": "bool", "name": "isLocked"}]}]}]}, {"type": "function", "name": "getToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}], "outputs": [{"type": "tuple", "name": "renewableEnergyTokenData", "components": [{"type": "uint8", "name": "tokenStatus"}, {"type": "bytes32", "name": "metadataId"}, {"type": "bytes32", "name": "metadataHash"}, {"type": "bytes32", "name": "mintAccountId"}, {"type": "bytes32", "name": "ownerAccountId"}, {"type": "bytes32", "name": "previousAccountId"}, {"type": "bool", "name": "isLocked"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getTokenCount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": "count"}]}, {"type": "function", "name": "getTokenIdsByAccountIdAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "tokenIdsByAccountId", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32[]", "name": "tokenIds"}]}]}, {"type": "function", "name": "getTokenList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "string", "name": "sortOrder"}], "outputs": [{"type": "tuple[]", "name": "renewableEnergyTokenList", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "uint8", "name": "tokenStatus"}, {"type": "bytes32", "name": "metadataId"}, {"type": "bytes32", "name": "metadataHash"}, {"type": "bytes32", "name": "mintAccountId"}, {"type": "bytes32", "name": "ownerAccountId"}, {"type": "bytes32", "name": "previousAccountId"}, {"type": "bool", "name": "isLocked"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "hasToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}, {"type": "address", "name": "token"}], "outputs": []}, {"type": "function", "name": "mint", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "metadataId"}, {"type": "bytes32", "name": "metadataHash"}, {"type": "bytes32", "name": "mintAccountId"}, {"type": "bytes32", "name": "ownerAccountId"}, {"type": "bool", "name": "isLocked"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "restoreRenewableEnergyTokens", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "renewableEnergytokens", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "tuple", "name": "renewableEnergyTokenData", "components": [{"type": "uint8", "name": "tokenStatus"}, {"type": "bytes32", "name": "metadataId"}, {"type": "bytes32", "name": "metadataHash"}, {"type": "bytes32", "name": "mintAccountId"}, {"type": "bytes32", "name": "ownerAccountId"}, {"type": "bytes32", "name": "previousAccountId"}, {"type": "bool", "name": "isLocked"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreTokenIdsByAccountId", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "tokenIdsByAccountId", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32[]", "name": "tokenIds"}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setRenewableEnergyTokenAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "renewableEnergytokens", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "tuple", "name": "renewableEnergyTokenData", "components": [{"type": "uint8", "name": "tokenStatus"}, {"type": "bytes32", "name": "metadataId"}, {"type": "bytes32", "name": "metadataHash"}, {"type": "bytes32", "name": "mintAccountId"}, {"type": "bytes32", "name": "ownerAccountId"}, {"type": "bytes32", "name": "previousAccountId"}, {"type": "bool", "name": "isLocked"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setTokenIdsByAccountIdAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "tokenIdByAccountId", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32[]", "name": "tokenIds"}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "transfer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x7d387074838f8547147714aaf8744c0005f7ae0da4f48bf7fb9068b97a722b35", "receipt": {"to": null, "from": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266", "contractAddress": "0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512", "transactionIndex": 0, "gasUsed": "4126361", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x61d92b129df9b49bf85c2bb2a911396ae29a60ba3eb74769956e155d692b0e80", "blockNumber": 682, "cumulativeGasUsed": "4126361", "status": 1}}
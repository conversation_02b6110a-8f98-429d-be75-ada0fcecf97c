package com.decurret_dcp.dcjpy.bcclient.controller.validation.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Documented
@Constraint(validatedBy = {})
@Target({ ElementType.PARAMETER, ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
@Size(max = 128)
public @interface RequestId {
    String message() default "Invalid format.";

    Class<?>[] groups() default {};

    Class<?>[] payload() default {};
}

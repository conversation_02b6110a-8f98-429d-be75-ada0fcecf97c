package com.decurret_dcp.dcjpy.bcclient.controller.request;

import java.util.LinkedHashMap;
import java.util.Map;

import javax.validation.constraints.NotNull;

import org.springframework.lang.Nullable;

import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand;
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId;
import com.decurret_dcp.dcjpy.bcclient.controller.validation.annotation.RequestId;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
public class SendRequest {

    @NotNull
    @RequestId
    public String requestId;

    @NotNull
    @com.decurret_dcp.dcjpy.bcclient.controller.validation.annotation.ZoneId
    public String zoneId;

    @NotNull
    public String contractName;

    @NotNull
    public String method;

    @Nullable
    public LinkedHashMap<String, Object> args;

    @Nullable
    public Boolean isAsync;

    public TransactionCommand initCommand(String traceId) {
        ZoneId zoneId = ZoneId.of(this.zoneId);

        LinkedHashMap<String, Object> params =
                (this.args != null) ? new LinkedHashMap<>(this.args) : new LinkedHashMap<>();

        return TransactionCommand.builder()
                .requestId(this.requestId)
                .zoneId(zoneId)
                .contractName(this.contractName)
                .method(this.method)
                .args(Map.copyOf(params))
                .traceId(traceId)
                .build();
    }

    public boolean isAsync() {
        if (this.isAsync == null) {
            return false;
        }

        return this.isAsync.booleanValue();
    }
}
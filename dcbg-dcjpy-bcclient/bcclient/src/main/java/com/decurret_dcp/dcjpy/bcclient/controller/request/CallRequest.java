package com.decurret_dcp.dcjpy.bcclient.controller.request;

import java.util.LinkedHashMap;
import java.util.Map;

import javax.validation.constraints.NotNull;

import org.springframework.lang.Nullable;

import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand;
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class CallRequest {

    @NotNull
    @com.decurret_dcp.dcjpy.bcclient.controller.validation.annotation.ZoneId
    public String zoneId;

    @NotNull
    public String contractName;

    @NotNull
    public String method;

    @Nullable
    public LinkedHashMap<String, Object> args;

    public TransactionCommand initInput() {
        ZoneId zoneId = ZoneId.of(this.zoneId);
        return TransactionCommand.builder()
                .zoneId(zoneId)
                .contractName(this.contractName)
                .method(this.method)
                .args((this.args != null) ? Map.copyOf(this.args) : Map.of())
                .build();
    }
}
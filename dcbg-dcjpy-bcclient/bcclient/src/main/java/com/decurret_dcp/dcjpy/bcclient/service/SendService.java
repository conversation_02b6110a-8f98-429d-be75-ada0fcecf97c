package com.decurret_dcp.dcjpy.bcclient.service;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

import com.decurret_dcp.dcjpy.bcclient.adaptor.AwsSqsAdaptor;
import com.decurret_dcp.dcjpy.bcclient.base.adaptor.AwsDynamoDbAdaptor;
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand;
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionResolver;
import com.decurret_dcp.dcjpy.bcclient.service.param.SendResult;
import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthTransactionCommand;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionResultValue;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionStatus;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionValue;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@RequiredArgsConstructor
@Service
@Slf4j
public class SendService {

    private final TransactionResolver transactionResolver;

    private final WakeUpHandler wakeUpHandler;

    private final AwsDynamoDbAdaptor dynamoDbAdaptor;

    private final AwsSqsAdaptor sqsAdaptor;

    /**
     * SendServiceを実行する
     *
     * @param command サービスインプット
     * @return SendResult
     */
    @NonNull
    public SendResult execute(TransactionCommand command, boolean isAsync) {
        EthTransactionCommand ethCommand = this.transactionResolver.resolve(command);

        // 渡された引数に漏れがないか検証する
        ethCommand.verify();

        this.dynamoDbAdaptor.register(ethCommand);

        // DynamoDB に send transaction の保存が完了してから、SQS に requestId をキューイングする
        try {
            this.sqsAdaptor.send(command.requestId);
        } catch (RuntimeException exc) {
            this.dynamoDbAdaptor.failed(command.requestId);
            throw exc;
        }

        if (isAsync == true) {
            return new SendResult(true, "", "");
        }

        Thread thread = Thread.currentThread();

        TransactionValue transaction;
        try {
            while (true) {
                // ブロック確定は通常 2 秒程度かかるので、ブロック確定のみ込み時間だけ待つ
                this.wakeUpHandler.onWaiting(thread);
                LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(2000L));

                transaction = this.dynamoDbAdaptor.findTransaction(command.requestId);
                if (transaction.transactionHash != null) {
                    break;
                }
            }
        } finally {
            this.wakeUpHandler.remove(thread);
        }

        // transaction_queue に既に結果が保存されている場合は、それを戻り値に利用する
        if (transaction.transactionStatus.isEnd()) {
            boolean result = (transaction.transactionStatus == TransactionStatus.COMPLETED) ? true : false;
            String revertReason = StringUtils.hasText(transaction.revertReason) ? transaction.revertReason : "";

            return new SendResult(result, revertReason, transaction.transactionHash);
        }

        TransactionResultValue resultValue;
        while (true) {
            resultValue = this.dynamoDbAdaptor.findTransactionResult(transaction.transactionHash);
            if (resultValue != null) {
                break;
            }

            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(300L));
        }

        boolean result = (resultValue.transactionStatus == TransactionStatus.COMPLETED) ? true : false;
        String revertReason = StringUtils.hasText(resultValue.revertReason) ? resultValue.revertReason : "";

        return new SendResult(result, revertReason, resultValue.transactionHash);
    }
}

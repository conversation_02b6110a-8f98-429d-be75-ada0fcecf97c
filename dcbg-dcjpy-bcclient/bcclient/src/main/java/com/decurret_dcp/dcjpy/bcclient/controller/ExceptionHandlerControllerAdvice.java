package com.decurret_dcp.dcjpy.bcclient.controller;

import javax.validation.ValidationException;

import com.decurret_dcp.dcjpy.bcclient.base.exception.BadRequestException;
import com.decurret_dcp.dcjpy.bcclient.base.exception.BlockchainIOException;
import com.decurret_dcp.dcjpy.bcclient.base.exception.NoRevertReasonException;
import com.decurret_dcp.dcjpy.bcclient.base.exception.RequestIdDuplicatedException;
import com.decurret_dcp.dcjpy.bcclient.base.exception.ResourceNotFoundException;
import com.decurret_dcp.dcjpy.bcclient.controller.response.SendResponse;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import lombok.extern.slf4j.Slf4j;

@ControllerAdvice
@Slf4j
public class ExceptionHandlerControllerAdvice extends ResponseEntityExceptionHandler {

    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<GenericErrorResponse> handleBadRequest(BadRequestException ex) {
        log.warn("Handles bad request. {}", ex.getMessage());
        HttpStatus status = HttpStatus.BAD_REQUEST;
        return new ResponseEntity<>(createResponse(status, ex), status);
    }

    @ExceptionHandler(RequestIdDuplicatedException.class)
    public ResponseEntity<SendResponse> handleRequestIdDuplicated(RequestIdDuplicatedException ex) {
        log.warn("Handles duplicated requestId request. {}", ex.getMessage());
        return new ResponseEntity<>(SendResponse.duplicatedRequestId(), HttpStatus.CONFLICT);
    }

    @ExceptionHandler(BlockchainIOException.class)
    public ResponseEntity<GenericErrorResponse> handleBadRequest(BlockchainIOException ex) {
        log.error("Handles bad request", ex);
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        return new ResponseEntity<>(createResponse(status, ex), status);
    }

    @ExceptionHandler(NoRevertReasonException.class)
    public ResponseEntity<GenericErrorResponse> handleNoRevertReasonTransaction(NoRevertReasonException ex) {
        log.warn("Handles no revert reason transaction result. {}", ex.getMessage());
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        return new ResponseEntity<>(createResponse(status, ex), status);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<Object> handleResourceNotFound() {
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<GenericErrorResponse> handleValidationException(ValidationException ex) {
        log.warn("Handles bad request. {}", ex.getMessage());
        HttpStatus status = HttpStatus.BAD_REQUEST;
        return new ResponseEntity<>(createResponse(status, ex), status);
    }

    @Override

    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatus status, WebRequest request
    ) {
        log.warn("Handles method argument not valid. {}", ex.getMessage());
        String message = "";
        FieldError fieldError = ex.getBindingResult().getFieldError();
        if (fieldError != null) {
            message = fieldError.getField() + ":" + fieldError.getDefaultMessage();
        }
        return new ResponseEntity<>(new GenericErrorResponse(
                status.value(),
                status.getReasonPhrase(),
                message
        ), status);
    }

    private GenericErrorResponse createResponse(HttpStatus status, Exception ex) {
        return new GenericErrorResponse(status.value(), status.getReasonPhrase(), ex.getMessage());
    }
}

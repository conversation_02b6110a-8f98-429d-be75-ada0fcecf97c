package com.decurret_dcp.dcjpy.bcclient.controller.validation.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Documented
@Constraint(validatedBy = {})
@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
@Min(3000)
@Max(3999)
public @interface ZoneId {
    String message() default "Invalid format.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}

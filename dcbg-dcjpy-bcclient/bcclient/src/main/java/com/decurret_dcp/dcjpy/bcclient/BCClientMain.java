package com.decurret_dcp.dcjpy.bcclient;

import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty;
import com.decurret_dcp.dcjpy.bcclient.base.properties.DynamoDbProperty;
import com.decurret_dcp.dcjpy.bcclient.base.properties.S3Property;
import com.decurret_dcp.dcjpy.bcclient.base.properties.SqsProperty;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.PostConstruct;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.reflect.Field;

@Slf4j
@EnableScheduling
@SpringBootApplication
@EnableConfigurationProperties({ S3Property.class, SqsProperty.class, DynamoDbProperty.class })
@ServletComponentScan
public class BCClientMain {

    private final ApplicationProperty property;

    private final SqsProperty sqsProperties;

    @Autowired
    public BCClientMain(ApplicationProperty property, SqsProperty sqsProperties) {
        this.property = property;
        this.sqsProperties = sqsProperties;
    }

    public static void main(String[] args) {
        // API Server
        SpringApplication.run(BCClientMain.class, args);
    }

    @PostConstruct
    private void init() throws IllegalAccessException {
        this.printHeapMemory();
        this.printApplicationProperties();
    }

    /**
     * HeapMemory設定を出力する
     */
    private void printHeapMemory() {
        int mb = 1024 * 1024;
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long xms = memoryBean.getHeapMemoryUsage().getInit() / mb;
        long xmx = memoryBean.getHeapMemoryUsage().getMax() / mb;
        log.info("Heap Initial (xms) : " + xms + "mb");
        log.info("Heap Max (xmx) : " + xmx + "mb");
    }

    /**
     * アプリケーションプロパティ設定を出力する
     */
    private void printApplicationProperties() throws IllegalAccessException {
        log.info("[application properties: start]");
        for (Field field : this.property.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            log.info("{}: {}", field.getName(), field.get(this.property));
            field.setAccessible(false);
        }
        log.info("[application properties: end]");

        log.info("[sqs properties: start]");
        for (Field field : this.sqsProperties.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            log.info("{}: {}", field.getName(), field.get(this.sqsProperties));
            field.setAccessible(false);
        }
        log.info("[sqs properties: end]");
    }
}

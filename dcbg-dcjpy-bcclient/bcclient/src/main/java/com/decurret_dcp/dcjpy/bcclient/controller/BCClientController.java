package com.decurret_dcp.dcjpy.bcclient.controller;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.decurret_dcp.dcjpy.bcclient.controller.request.CallRequest;
import com.decurret_dcp.dcjpy.bcclient.controller.request.SendRequest;
import com.decurret_dcp.dcjpy.bcclient.controller.response.CallResponse;
import com.decurret_dcp.dcjpy.bcclient.controller.response.GetResponse;
import com.decurret_dcp.dcjpy.bcclient.controller.response.SendResponse;
import com.decurret_dcp.dcjpy.bcclient.controller.validation.annotation.RequestId;
import com.decurret_dcp.dcjpy.bcclient.service.CallService;
import com.decurret_dcp.dcjpy.bcclient.service.FindService;
import com.decurret_dcp.dcjpy.bcclient.service.SendService;
import com.decurret_dcp.dcjpy.bcclient.service.param.CallResult;
import com.decurret_dcp.dcjpy.bcclient.service.param.FindResult;
import com.decurret_dcp.dcjpy.bcclient.service.param.SendResult;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Controller
@Slf4j
@Validated
public class BCClientController {

    private final CallService callService;

    private final SendService sendService;

    private final FindService findService;

    @RequestMapping(value = "/call", method = RequestMethod.POST, headers = "Accept=application/json")
    @ResponseBody
    public CallResponse call(@RequestBody @Validated CallRequest request) {
        CallResult callResult = this.callService.execute(request.initInput());

        return CallResponse.create(callResult);
    }

    @RequestMapping(value = "/send", method = RequestMethod.POST, headers = "Accept=application/json")
    @ResponseBody
    public SendResponse send(
            @RequestHeader(name = "trace-id", required = false) String traceId,
            @RequestBody @Validated SendRequest request
    ) {
        SendResult sendResult = this.sendService.execute(request.initCommand(traceId), request.isAsync());

        return SendResponse.create(sendResult);
    }

    @RequestMapping(value = "/get/{requestId}", method = RequestMethod.GET, headers = "Accept=application/json")
    @ResponseBody
    public GetResponse get(@PathVariable("requestId") @RequestId String requestId) {
        FindResult findResult = this.findService.find(requestId);

        return GetResponse.create(findResult);
    }
}

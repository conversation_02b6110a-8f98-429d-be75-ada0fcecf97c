package com.decurret_dcp.dcjpy.bcclient.controller;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.MDC;
import org.springframework.util.StringUtils;

@WebFilter("/*")
public class TraceIdRecordFilter extends HttpFilter {

    @Override
    protected void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        String traceId = request.getHeader("trace-id");
        if (StringUtils.hasText(traceId)) {
            MDC.put("traceId", traceId);
        }

        try {
            super.doFilter(request, response, chain);
        } finally {
            MDC.remove("traceId");
        }
    }
}

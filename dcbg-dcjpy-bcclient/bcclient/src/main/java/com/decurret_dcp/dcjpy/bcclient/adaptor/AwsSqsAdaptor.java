package com.decurret_dcp.dcjpy.bcclient.adaptor;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bcclient.base.properties.SqsProperty;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest;
import software.amazon.awssdk.services.sqs.model.GetQueueUrlResponse;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;
import software.amazon.awssdk.services.sqs.model.SqsException;

@RequiredArgsConstructor
@Slf4j
public class AwsSqsAdaptor {

    private static final String SQS_MESSAGE_GROUP_ID = "dcjpy_bcclient_send_transaction_sqs";

    private final SqsClient sqsClient;

    private final String queueUrl;

    public static AwsSqsAdaptor create(SqsClient sqsClient, SqsProperty property) {
        String queueUrl = initQueueUrl(sqsClient, property.queueName);
        return new AwsSqsAdaptor(sqsClient, queueUrl);
    }

    private static String initQueueUrl(SqsClient sqsClient, String queueName) {
        GetQueueUrlRequest requestUrl = GetQueueUrlRequest.builder()
                .queueName(queueName)
                .build();
        GetQueueUrlResponse responseUrl = sqsClient.getQueueUrl(requestUrl);

        return responseUrl.queueUrl();
    }

    public void send(String requestId) {
        SendMessageRequest request = SendMessageRequest.builder()
                .queueUrl(this.queueUrl)
                .messageBody(requestId)
                .messageGroupId(SQS_MESSAGE_GROUP_ID)
                .messageDeduplicationId(requestId)
                .build();

        try {
            SendMessageResponse response = this.sqsClient.sendMessage(request);
            log.debug("Succeed to send to sqs for requestId = {}, messageId = {}", requestId, response.messageId());
        } catch (SqsException sqsExc) {
            log.error("Failed to send to sqs for requestId = {}", requestId, sqsExc);
            throw sqsExc;
        }
    }
}

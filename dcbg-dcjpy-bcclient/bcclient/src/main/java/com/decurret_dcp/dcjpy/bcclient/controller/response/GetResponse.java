package com.decurret_dcp.dcjpy.bcclient.controller.response;

import com.decurret_dcp.dcjpy.bcclient.service.param.FindResult;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class GetResponse {

    public final String requestId;

    public final String transactionStatus;

    public final String transactionHash;

    public final String revertReason;

    public static GetResponse create(FindResult result) {
        return GetResponse.builder()
                .requestId(result.requestId)
                .transactionStatus(result.transactionStatus.getValue())
                .transactionHash(result.transactionHash)
                .revertReason(result.revertReason)
                .build();
    }
}

package com.decurret_dcp.dcjpy.bcclient.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.decurret_dcp.dcjpy.bcclient.adaptor.AwsSqsAdaptor;
import com.decurret_dcp.dcjpy.bcclient.base.controller.CustomHealthIndicator;
import com.decurret_dcp.dcjpy.bcclient.base.credential.Web3jCredentialHolder;
import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty;
import com.decurret_dcp.dcjpy.bcclient.base.properties.SqsProperty;
import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthCallExecutor;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.SubWebSocketConnectionPool;
import com.decurret_dcp.dcjpy.bcclient.service.WakeUpHandler;

import software.amazon.awssdk.services.sqs.SqsClient;

@Configuration
public class BCClientConfig {

    @Bean
    public AwsSqsAdaptor awsSqsAdaptor(SqsClient sqsClient, SqsProperty property) {
        return AwsSqsAdaptor.create(sqsClient, property);
    }

    @Bean
    public MainWebSocketConnectionPool mainWebSocketConnectionPool(
            ApplicationProperty properties, WakeUpHandler wakeUpHandler
    ) {
        return new MainWebSocketConnectionPool(properties, wakeUpHandler);
    }

    @Bean
    public SubWebSocketConnectionPool subWebSocketConnectionPool(ApplicationProperty properties) {
        return new SubWebSocketConnectionPool(properties);
    }

    @Bean
    public EthCallExecutor ethCallExecutor() {
        Web3jCredentialHolder credentialHolder = new Web3jCredentialHolder();
        return new EthCallExecutor(credentialHolder);
    }

    @Bean
    public CustomHealthIndicator customHealthIndicator(
            ApplicationProperty properties, MainWebSocketConnectionPool mainWebSocketConnectionPool,
            SubWebSocketConnectionPool subWebSocketConnectionPool
    ) {
        return new CustomHealthIndicator(properties, mainWebSocketConnectionPool, subWebSocketConnectionPool);
    }

    @Bean
    public BCClientAccessLoggingFilter loggingFilter() {
        return new BCClientAccessLoggingFilter();
    }
}

package com.decurret_dcp.dcjpy.bcclient.service;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.decurret_dcp.dcjpy.bcclient.base.adaptor.AwsDynamoDbAdaptor;
import com.decurret_dcp.dcjpy.bcclient.base.exception.ResourceNotFoundException;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionResultValue;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionValue;
import com.decurret_dcp.dcjpy.bcclient.service.param.FindResult;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class FindService {

    private final AwsDynamoDbAdaptor dynamoDbAdaptor;

    public FindResult find(String requestId) {
        TransactionValue transaction;
        try {
            transaction = this.dynamoDbAdaptor.findTransaction(requestId);
        } catch (software.amazon.awssdk.services.dynamodb.model.ResourceNotFoundException notFound) {
            throw new ResourceNotFoundException("transaction not found. requestId = " + requestId, notFound);
        }

        if (transaction.transactionStatus.isEnd()) {
            return initResult(transaction);
        }

        // トランザクションハッシュが存在しない場合は受付テーブルの値を返す
        if (StringUtils.hasText(transaction.transactionHash) == false) {
            return initResult(transaction);
        }

        TransactionResultValue result = this.dynamoDbAdaptor.findTransactionResult(transaction.transactionHash);
        if (result == null) {
            return initResult(transaction);
        }

        return FindResult.builder()
                .requestId(transaction.requestId)
                .transactionStatus(result.transactionStatus)
                .transactionHash(transaction.transactionHash)
                .revertReason(result.revertReason)
                .build();
    }

    private static FindResult initResult(TransactionValue transaction) {
        return FindResult.builder()
                .requestId(transaction.requestId)
                .transactionStatus(transaction.transactionStatus)
                .transactionHash(transaction.transactionHash)
                .revertReason(transaction.revertReason)
                .build();
    }
}

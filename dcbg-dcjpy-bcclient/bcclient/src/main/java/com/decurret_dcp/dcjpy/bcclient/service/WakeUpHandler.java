package com.decurret_dcp.dcjpy.bcclient.service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.LockSupport;

import org.springframework.stereotype.Component;
import org.web3j.protocol.core.methods.response.EthBlock;

import com.decurret_dcp.dcjpy.bcclient.base.websocket.BcEventHandler;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.WebSocketConnectionPoolBase;

/**
 * DLT のブロック確定のイベントを契機として、保持しているスレッドを再開させる。
 * send transaction を同期的に処理する際のブロック確定までの待ち処理を効率的に行うためのクラス。
 */
@Component
public class WakeUpHandler implements BcEventHandler {

    private final ConcurrentHashMap<Long, Thread> waitingTreads = new ConcurrentHashMap<>();

    @Override
    public void onEvent(WebSocketConnectionPoolBase connectionPool, EthBlock ethBlock) {
        this.waitingTreads.forEachKey(1, key -> {
            Thread thread = this.waitingTreads.remove(key);
            if (thread == null) {
                return;
            }

            LockSupport.unpark(thread);
        });
    }

    public void onWaiting(Thread thread) {
        Long threadId = Long.valueOf(thread.getId());
        this.waitingTreads.put(threadId, thread);
    }

    public void remove(Thread thread) {
        Long threadId = Long.valueOf(thread.getId());
        this.waitingTreads.remove(threadId);
    }
}

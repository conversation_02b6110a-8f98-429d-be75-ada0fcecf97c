package com.decurret_dcp.dcjpy.bcclient.controller.response;

import com.decurret_dcp.dcjpy.bcclient.service.param.SendResult;

import lombok.Data;

@Data
public class SendResponse {

    private final boolean result;

    private final String error;

    private final String transactionHash;

    public static SendResponse create(SendResult serviceOutput) {
        boolean result = serviceOutput.isResult();
        String error = serviceOutput.getError();
        String transactionHash = serviceOutput.getTransactionHash();

        return new SendResponse(result, error, transactionHash);
    }

    public static SendResponse duplicatedRequestId() {
        return new SendResponse(false, "99999:request_id duplicated.","");
    }
}

package com.decurret_dcp.dcjpy.bcclient.service.param;

import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionStatus;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class FindResult {

    public final String requestId;

    public final TransactionStatus transactionStatus;

    public final String transactionHash;

    public final String revertReason;
}

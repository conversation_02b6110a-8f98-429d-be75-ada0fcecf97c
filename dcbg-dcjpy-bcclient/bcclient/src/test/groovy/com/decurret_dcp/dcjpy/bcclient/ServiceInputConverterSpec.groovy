package com.decurret_dcp.dcjpy.bcclient

import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractAbi
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractFunction
import com.decurret_dcp.dcjpy.bcclient.base.exception.BadRequestException
import com.decurret_dcp.dcjpy.bcclient.base.exception.ContractNotFoundException
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand
import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthTransactionCommand
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId

// TODO bcclient-base モジュールだと ApplicationProperties, LocalProperties をインジェクトできないため、やむえずモジュール移動
class ServiceInputConverterSpec extends IntegrationTestBase {

    def "Convert: 変換が正常に実行されること"() {
        when:
        TransactionCommand input = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        ContractAbi contractAbi = this.contractInfo.getContractAbi(input.zoneId, input.getContractName())
        ContractFunction contractFunction = contractAbi.getFunction(input.getMethod())

        EthTransactionCommand command = EthTransactionCommand.builder()
                .contractAbi(contractAbi)
                .contractFunction(contractFunction)
                .parameters(args)
                .build()

        then:
        command.contractAddress() == contractAddress
        command.encode() == encodedFunction

        where:
        contractName | method            | args                                                                                              || contractAddress                              | encodedFunction
        "Validator"  | "hasValidatorID"  | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
                put("chkEnabled", true)
            }
        }                                                                                                                                    || "******************************************" | "0xcc816c88000000000000000000000000000000000000000000000000000000000000007b0000000000000000000000000000000000000000000000000000000000000001"
        "Provider"   | "hasProvUserID"   | new LinkedHashMap<String, Object>() {
            {
                put("provID", 123)
                put("userID", 321)
            }
        }                                                                                                                                    || "0xf17e6cA26072E0668d1a24F5Be91f45118B6E30c" | "0x0a17cdbe000000000000000000000000000000000000000000000000000000000000007b0000000000000000000000000000000000000000000000000000000000000141"
        "Provider"   | "addProviderRole" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 123)
                put("eoaProv", "0x00000400")
                put("deadline", **********)
                put("signature", (byte[]) [49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
            }
        }                                                                                                                                    || "0xf17e6cA26072E0668d1a24F5Be91f45118B6E30c" | "0xe15a3d34000000000000000000000000000000000000000000000000000000000000007b00000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000060c317e3000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000203100000000000000000000000000000000000000000000000000000000000000"
    }

    def "Convert: メソッド名が存在しない場合にContractNotFoundExceptionが発生すること"() {
        when:
        TransactionCommand input = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        ContractAbi contractAbi = this.contractInfo.getContractAbi(input.zoneId, input.getContractName())
        ContractFunction contractFunction = contractAbi.getFunction(input.getMethod())

        then:
        thrown(ex)

        where:
        contractName | method | args                          | ex
        "Validator"  | "none" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
                put("chkEnabled", true)
            }
        }                                                     | ContractNotFoundException
    }

    def "Convert: 引数不足の場合にBadRequestExceptionが発生すること"() {
        when:
        TransactionCommand input = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        ContractAbi contractAbi = this.contractInfo.getContractAbi(input.zoneId, input.getContractName())
        ContractFunction contractFunction = contractAbi.getFunction(input.getMethod())

        EthTransactionCommand command = EthTransactionCommand.builder()
                .contractAbi(contractAbi)
                .contractFunction(contractFunction)
                .parameters(args)
                .build()
        command.encode()

        then:
        thrown(BadRequestException)

        where:
        contractName | method            | args
        "Validator"  | "hasValidatorID"  | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
            }
        }
        "Provider"   | "addProviderRole" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 123)
                put("eoaProv", "0x00000400")
                put("deadline", **********)
            }
        }
    }
}

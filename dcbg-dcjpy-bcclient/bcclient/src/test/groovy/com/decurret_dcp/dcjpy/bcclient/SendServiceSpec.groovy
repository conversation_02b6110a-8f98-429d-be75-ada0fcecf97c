package com.decurret_dcp.dcjpy.bcclient

import com.decurret_dcp.dcjpy.bcclient.base.adaptor.AwsDynamoDbAdaptor
import com.decurret_dcp.dcjpy.bcclient.base.service.ExternalAbiResolver
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand
import com.decurret_dcp.dcjpy.bcclient.service.SendService

import com.decurret_dcp.dcjpy.bcclient.service.param.SendResult
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractAbi
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractFunction
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractInfo
import com.decurret_dcp.dcjpy.bcclient.base.exception.BadRequestException

import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId
import org.mockito.Mockito
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.SpyBean
import org.web3j.protocol.core.methods.response.EthSendTransaction

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.doNothing
import static org.mockito.Mockito.doReturn
import static org.mockito.Mockito.when

// TODO bcclient-base モジュールだと ApplicationProperties, LocalProperties をインジェクトできないため、やむえずモジュール移動
class SendServiceSpec extends IntegrationTestBase {

    @SpyBean
    private ExternalAbiResolver externalAbiResolver

    @Autowired
    private SendService sendService

    @SpyBean
    private AwsDynamoDbAdaptor dynamoDbAdaptor;

    @SpyBean
    private ContractInfo contractInfo

    private static final String TRANSACTION_HASH = "0x11"

    def "setup"() {
        EthSendTransaction ethSendTransaction = Mockito.mock(EthSendTransaction.class)

        when(ethSendTransaction.getError())
                .thenReturn(null)

        when(ethSendTransaction.getTransactionHash())
                .thenReturn(TRANSACTION_HASH)
    }

    def "Execute: SendServiceが正常に実行できること"() {
        when:
        doNothing()
                .when(this.dynamoDbAdaptor).register(notNull())

        doNothing()
                .when(this.dynamoDbAdaptor).failed(notNull())

        TransactionCommand command = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        SendResult sendServiceOutput = this.sendService.execute(command, true)

        then:
        sendServiceOutput.isResult() == true
        sendServiceOutput.getError() == ""
        sendServiceOutput.getTransactionHash() != null
        sendServiceOutput.getTransactionHash() == ""

        where:
        contractName | method             | args
        "Validator"  | "getValidatorData" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 4369)
            }
        }
    }

    def "Execute: コントラクト名・メソッド名が存在しない場合と引数不足の場合にBadRequestExceptionが発生すること"() {
        when:
        doReturn(new ContractAbi("name", "0x00", new ArrayList<ContractFunction>()))
                .when(this.externalAbiResolver).resolve((ZoneId) notNull(), (String) notNull())
        TransactionCommand command = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        this.sendService.execute(command, true)

        then:
        thrown(ex)

        where:
        contractName | method     | args                          | ex
        "none"       | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }                                                         | BadRequestException
        "Validator"  | "none"     | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }                                                         | BadRequestException
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
            }
        }                                                         | BadRequestException
    }
}

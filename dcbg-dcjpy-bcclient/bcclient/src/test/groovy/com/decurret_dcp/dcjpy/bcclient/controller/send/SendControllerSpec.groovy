// TODO 別途テストコードを整理する
package com.decurret_dcp.dcjpy.bcclient.controller.send

import com.decurret_dcp.dcjpy.bcclient.BCClientMain
import com.decurret_dcp.dcjpy.bcclient.adaptor.AwsSqsAdaptor
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractInfo
import com.decurret_dcp.dcjpy.bcclient.base.exception.BadRequestException
import com.decurret_dcp.dcjpy.bcclient.base.exception.BlockchainIOException
import com.decurret_dcp.dcjpy.bcclient.base.exception.NoRevertReasonException
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool
import com.decurret_dcp.dcjpy.bcclient.base.websocket.SubWebSocketConnectionPool
import com.decurret_dcp.dcjpy.bcclient.controller.BCClientController
import com.decurret_dcp.dcjpy.bcclient.controller.request.SendRequest
import com.decurret_dcp.dcjpy.bcclient.service.SendService
import com.decurret_dcp.dcjpy.bcclient.service.param.SendResult
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.eq
import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.when

@ActiveProfiles("test")
@WebMvcTest(BCClientController.class)
@ComponentScan(basePackages = "com.decurret_dcp.dcjpy.bcclient")
class SendControllerSpec extends Specification {

    @MockBean
    private BCClientMain application

    @MockBean
    private SendService sendService

    @MockBean
    private ContractInfo contractInfo

    @MockBean
    private AwsSqsAdaptor awsSqsAdaptor

    @MockBean
    private MainWebSocketConnectionPool mainWebSocketConnectionPool

    @MockBean
    private SubWebSocketConnectionPool subWebSocketConnectionPool

    @Autowired
    private MockMvc mockMvc

    def "Send: リクエストが正常に処理されステータス200が返ってくること"() {
        when:
        when(sendService.execute((TransactionCommand) notNull(), eq(false)))
                .thenReturn(new SendResult(true, "", "0x00"))

        SendRequest param = new SendRequest("requestId", "3000", contractName, method, args, Boolean.FALSE)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isOk())

        where:
        contractName | method     | args
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }
    }

    def "Send: Transferリクエストが通常のSendリクエストで実行されること"() {
        when:
        when(sendService.execute((TransactionCommand) notNull(), eq(false)))
                .thenReturn(new SendResult(true, "", "0x00"))

        SendRequest param = new SendRequest("requestId", "3000", contractName, method, args, Boolean.FALSE)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isOk())

        where:
        contractName | method           | args
        "Token"      | "transferSingle" | new LinkedHashMap<String, Object>()
    }

    def "Send: BadRequestExceptionが発生した時に400エラーが返ってくること"() {
        when:
        when(sendService.execute((TransactionCommand) notNull(), eq(false)))
                .thenThrow(BadRequestException)

        SendRequest param = new SendRequest("requestId", "3000", contractName, method, args, Boolean.FALSE)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isBadRequest())

        where:
        contractName | method     | args
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }
    }

    def "Send: BlockchainIOExceptionが発生した時に500エラーが返ってくること"() {
        when:
        when(sendService.execute((TransactionCommand) notNull(), eq(false)))
                .thenThrow(BlockchainIOException)

        SendRequest param = new SendRequest("requestId", "3000", contractName, method, args, Boolean.FALSE)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isInternalServerError())

        where:
        contractName | method     | args
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }
    }

    def "Send: NoRevertReasonExceptionが発生した時に500エラーが返ってくること"() {
        when:
        when(sendService.execute((TransactionCommand) notNull(), eq(false)))
                .thenThrow(NoRevertReasonException)

        SendRequest param = new SendRequest("requestId", "3000", contractName, method, args, Boolean.FALSE)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isInternalServerError())

        where:
        contractName | method     | args
        "Validator"  | "Activate" | new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }
    }

    def "Send: return BadRequest when #testCase"() {
        when:
        def args = new LinkedHashMap<String, Object>() {
            {
                put("provID", 4369)
                put("validID", 13107)
                put("active", true)
            }
        }
        SendRequest param = new SendRequest(requestId, zoneId, "Validator", "Activate", args, Boolean.FALSE)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isBadRequest())

        where:
        testCase                            | requestId   | zoneId
        "zoneId is less than minimum"       | "requestId" | "2999"
        "zoneId is greater than maximum"    | "requestId" | "4000"
        "zoneId is null"                    | "requestId" | null
        "zoneId is not a number"            | "requestId" | "zoneId"
        "requestId is null"                 | null        | "3000"
        "requestId is greater than maximum" | "r" * 129   | "3000"
    }

    def "Send: リクエストが正常に処理されステータス200が返ってくること - contractName: #vContractName - vMethod: #vMethod"() {
        when:
        when(sendService.execute((TransactionCommand) notNull(), eq(false)))
                .thenReturn(new SendResult(true, "", "0x00"))

        SendRequest param = new SendRequest("requestId", "3000", vContractName, vMethod, vArgs, Boolean.FALSE)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/send")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isOk())

        where:
        vContractName | vMethod       | vArgs
        "Validator"  | "addAccount" | new LinkedHashMap<String, Object>() {
            {
                put("validatorId", 4369)
                put("accountId", 13107)
                put("accountName", "accountName")
                put("limitAmounts", [1, 2])
                put("traceId", "traceId")
            }
        }
        "Validator"  | "modAccount" | new LinkedHashMap<String, Object>() {
            {
                put("accountId", 1)
                put("accountName", "accountName")
                put("traceId", "traceId")
                put("deadline", **********)
                put("signature", "signature")
            }
        }
        "Issuer"  | "modTokenLimit" | new LinkedHashMap<String, Object>() {
            {
                put("issuerId", 1)
                put("accountId", 1)
                put("itemFlgs", [true, true, true, true, true])
                put("limitAmounts", [1, 2, 3, 4, 5])
                put("deadline", **********)
                put("signature", "signature")
                put("traceId", "traceId")
            }
        }
        "Issuer"  | "cumulativeReset" | new LinkedHashMap<String, Object>() {
            {
                put("issuerId", 1)
                put("accountId", 1)
                put("traceId", "traceId")
                put("deadline", **********)
                put("signature", "signature")
            }
        }
        "Issuer"  | "setAccountStatus" | new LinkedHashMap<String, Object>() {
            {
                put("issuerId", 1)
                put("accountId", 1)
                put("accountStatus", 1)
                put("reasonCode", "reasonCode")
                put("traceId", "traceId")
                put("deadline", **********)
                put("signature", "signature")
            }
        }
        "AccountSyncBridge" | "syncAccount" | new LinkedHashMap<String, Object>() {
            {
                put("validatorId", 1)
                put("accountId", 1)
                put("accountName", "accountName")
                put("fromZoneId", 1)
                put("zoneName", "zoneName")
                put("accountStatus", 1)
                put("reasonCode", 100)
                put("approveAmount", 10)
                put("traceId", "traceId")
                put("deadline", **********)
                put("timeoutHeight", **********)
                put("signature", "signature")
            }
        }
        "Issuer" | "forceBurn" | new LinkedHashMap<String, Object>() {
            {
                put("issuerId", 1)
                put("accountId", 1)
                put("traceId", "traceId")
                put("deadline", **********)
                put("signature", "signature")
            }
        }
        "Provider" | "addToken" | new LinkedHashMap<String, Object>() {
            {
                put("providerId", 1)
                put("tokenId", 1)
                put("name", "name")
                put("symbol", "symbol")
                put("traceId", "traceId")
                put("deadline", **********)
                put("signature", "signature")
            }
        }
        "Provider" | "modToken" | new LinkedHashMap<String, Object>() {
            {
                put("tokenId", 1)
                put("name", "name")
                put("symbol", "symbol")
                put("traceId", "traceId")
                put("deadline", **********)
                put("signature", "signature")
            }
        }
        "Token" | "mint" | new LinkedHashMap<String, Object>() {
            {
                put("tokenId", 1)
                put("accountId", 1)
                put("amount", 10)
                put("traceId", "traceId")
                put("deadline", **********)
                put("signature", "signature")
            }
        }
        "Token" | "burn" | new LinkedHashMap<String, Object>() {
            {
                put("issuerId", 1)
                put("accountId", 1)
                put("amount", 10)
                put("traceId", "traceId")
                put("deadline", **********)
                put("signature", "signature")
            }
        }
        "Token" | "burnCancel" | new LinkedHashMap<String, Object>() {
            {
                put("issuerId", 1)
                put("accountId", 1)
                put("amount", 10)
                put("blockTimestamp", **********)
                put("traceId", "traceId")
                put("deadline", **********)
                put("signature", "signature")
            }
        }
        "Token" | "approve" | new LinkedHashMap<String, Object>() {
            {
                put("validatorId", 1)
                put("ownerId", 1)
                put("spenderId", 1)
                put("amount", 10)
                put("traceId", "traceId")
                put("deadline", **********)
                put("signature", "signature")
            }
        }
    }
}

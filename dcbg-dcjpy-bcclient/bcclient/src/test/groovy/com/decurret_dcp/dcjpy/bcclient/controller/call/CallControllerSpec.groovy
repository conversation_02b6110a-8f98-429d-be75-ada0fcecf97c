// TODO 別途テストコードを整理する
package com.decurret_dcp.dcjpy.bcclient.controller.call

import com.decurret_dcp.dcjpy.bcclient.BCClientMain
import com.decurret_dcp.dcjpy.bcclient.adaptor.AwsSqsAdaptor
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractInfo
import com.decurret_dcp.dcjpy.bcclient.base.exception.BadRequestException
import com.decurret_dcp.dcjpy.bcclient.base.exception.BlockchainIOException
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool
import com.decurret_dcp.dcjpy.bcclient.base.websocket.SubWebSocketConnectionPool
import com.decurret_dcp.dcjpy.bcclient.controller.BCClientController
import com.decurret_dcp.dcjpy.bcclient.controller.request.CallRequest
import com.decurret_dcp.dcjpy.bcclient.service.CallService
import com.decurret_dcp.dcjpy.bcclient.service.param.CallResult
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.when

@ActiveProfiles("test")
@WebMvcTest(BCClientController.class)
@ComponentScan(basePackages = "com.decurret_dcp.dcjpy.bcclient")
class CallControllerSpec extends Specification {

    @MockBean
    private BCClientMain application

    @MockBean
    private CallService callService

    @MockBean
    private ContractInfo contractInfo

    @MockBean
    private AwsSqsAdaptor awsSqsAdaptor

    @MockBean
    private MainWebSocketConnectionPool mainWebSocketConnectionPool

    @MockBean
    private SubWebSocketConnectionPool subWebSocketConnectionPool

    @Autowired
    private MockMvc mockMvc

    def "Call: リクエストが正常に処理されステータス200が返ってくること"() {
        when:
        when(callService.execute((TransactionCommand) notNull()))
                .thenReturn(new CallResult(new LinkedHashMap()))

        CallRequest param = new CallRequest("3000", contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.post("/call")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isOk())

        where:
        contractName | method           | args
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }
    }

    def "Call: BadRequestExceptionが発生した時に400エラーが返ってくること"() {
        when:
        when(callService.execute((TransactionCommand) notNull()))
                .thenThrow(BadRequestException)

        CallRequest param = new CallRequest("3000", contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/call")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isBadRequest())

        where:
        contractName | method           | args
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }
    }

    def "Call: BlockchainIOExceptionが発生した時に500エラーが返ってくること"() {
        when:
        when(callService.execute((TransactionCommand) notNull()))
                .thenThrow(BlockchainIOException)

        CallRequest param = new CallRequest("3000", contractName, method, args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/call")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isInternalServerError())

        where:
        contractName | method           | args
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }
    }

    def "Call: return BadRequest when #testCase"() {
        when:
        def args = new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }
        CallRequest param = new CallRequest(zoneId, "Validator", "hasValidatorID", args)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.post("/call")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isBadRequest())

        where:
        testCase                 | zoneId
        "zoneId less 3000"       | "2999"
        "zoneId more 3999"       | "4000"
        "zoneId is null"         | null
        "zoneId is not a number" | "zoneId"
    }

    def "Call: リクエストが正常に処理されステータス200が返ってくること - contractName: #vContractName - method: #vMethod"() {
        when:
        when(callService.execute((TransactionCommand) notNull()))
                .thenReturn(vResult)

        CallRequest param = new CallRequest("3000", vContractName, vMethod, vArgs)
        ObjectMapper objectMapper = new ObjectMapper()

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.post("/call")
                        .content(objectMapper.writeValueAsString(param))
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isOk())

        where:
        vContractName | vMethod           | vArgs  | vResult
        "Validator"  | "getAccount" | new LinkedHashMap<String, Object>() {
            {
                put("validatorId", 1)
                put("accountId", 1)
            }
        } | new CallResult(new LinkedHashMap<String, Object>() {
            {
                put("accountData", new LinkedHashMap<String, Object>() {
                    {
                        put("accountName", "accountName")
                        put("accountStatus", 1)
                        put("balance", 100)
                        put("reasonCode", 1)
                        put("appliedAt", **********)
                        put("registeredAt", **********)
                        put("terminatingAt", **********)
                        put("terminatedAt", **********)
                        put("mintLimit", 1000000)
                        put("burnLimit", 1000001)
                        put("chargeLimit", 1000002)
                        put("transferLimit", 1000003)
                        put("cumulativeLimit", 1000004)
                        put("cumulativeAmount", 1000005)
                        put("cumulativeDate", **********)
                    }
                })
                put("err", "")
            }
        })
        "Token"  | "getBalanceList" | new LinkedHashMap<String, Object>() {
            {
                put("accountId", 1)
            }
        } | new CallResult(new LinkedHashMap<String, Object>() {
            {
                put("zoneIds", [3001, 3002])
                put("zoneNames", ["zoneName1", "zoneName2"])
                put("balance", [100, 200])
                put("accountNames", ["accountName1", "accountName2"])
                put("accountStatus", ["active", "active"])
                put("totalBalance", 2)
                put("err", "")
            }
        })
        "Validator"  | "getAccountAll" | new LinkedHashMap<String, Object>() {
            {
                put("validatorId", 1)
                put("accountId", 1)
            }
        } | new CallResult(new LinkedHashMap<String, Object>() {
            {
                put("accountData", new LinkedHashMap<String, Object>() {
                    {
                        put("accountName", "accountName")
                        put("accountStatus", 1)
                        put("balance", 100)
                        put("reasonCode", 1)
                        put("zoneId", 1)
                        put("zoneName", true)
                        put("appliedAt", **********)
                        put("registeredAt", **********)
                        put("terminatingAt", **********)
                        put("terminatedAt", **********)
                        put("mintLimit", 1000000)
                        put("burnLimit", 1000001)
                        put("chargeLimit", 1000002)
                        put("transferLimit", 1000003)
                        put("cumulativeLimit", 1000004)
                        put("cumulativeAmount", 1000005)
                        put("cumulativeDate", **********)
                        put("businessZoneAccounts", new LinkedHashMap<String, Object>() {
                            {
                                put("accountName", "accountName")
                                put("zoneId", 1)
                                put("zoneName", "zoneName")
                                put("balance", 100)
                                put("accountStatus", 1)
                                put("appliedAt", **********)
                                put("registeredAt", **********)
                                put("terminatingAt", **********)
                                put("terminatedAt", **********)
                            }
                        })
                    }
                })
                put("err", "")
            }
        })
        "FinancialCheck"  | "checkSyncAccount" | new LinkedHashMap<String, Object>() {
            {
                put("validatorId", 1)
                put("accountId", 1)
                put("zoneId", 1)
                put("accountStatus", 1)
                put("accountSignature", "accountSignature")
                put("info", "info")
            }
        } | new CallResult(new LinkedHashMap<String, Object>() {
            {
                put("err", "")
            }
        })
        "Validator"  | "getDestinationAccount" | new LinkedHashMap<String, Object>() {
            {
                put("accountId", 1)
            }
        } | new CallResult(new LinkedHashMap<String, Object>() {
            {
                put("accountName", "accountName")
                put("err", "")
            }
        })
        "FinancialCheck"  | "checkTransaction" | new LinkedHashMap<String, Object>() {
            {
                put("zoneId", 1)
                put("sendAccountId", 1)
                put("fromAccountId", 1)
                put("toAccountId", 1)
                put("amount", 100)
                put("accountSignature", "accountSignature")
                put("info", "info")
            }
        } | new CallResult(new LinkedHashMap<String, Object>() {
            {
                put("err", "")
            }
        })
        "Account"  | "getAllowance" | new LinkedHashMap<String, Object>() {
            {
                put("ownerId", 1)
                put("spenderId", 1)
            }
        } | new CallResult(new LinkedHashMap<String, Object>() {
            {
                put("allowance", 100)
                put("approvedAt", **********)
                put("spenderAccountName", "spenderAccountName")
                put("err", "")
            }
        })
        "BusinessZoneAccount" | "syncBusinessZoneBalance" | new LinkedHashMap<String, Object>() {
            {
                put("fromZoneId", 1)
                put("fromAccountId", 1)
                put("fromAccountName", "fromAccountName")
                put("toAccountId", 1)
                put("toAccountName", "toAccountName")
                put("traceId", "traceId")
            }
        } | new CallResult(new LinkedHashMap<String, Object>() {
            {
                put("traceId", "traceId")
                put("transferData", new LinkedHashMap<String, Object>() {
                    {
                        put("transferType", 1)
                        put("zoneId", 1)
                        put("fromValidatorId", 1)
                        put("toValidatorId", 1)
                        put("fromAccountBalance", 100)
                        put("toAccountBalance", 100)
                        put("businessZoneBalance", 100)
                        put("bizZoneId", 1)
                        put("sendAccountId", 1)
                        put("fromAccountId", 1)
                        put("fromAccountName", "fromAccountName")
                        put("toAccountId", 1)
                        put("toAccountName", "toAccountName")
                        put("amount", 100)
                        put("miscValue1", "miscValue1")
                        put("miscValue2", "miscValue2")
                        put("memo", "memo")
                    }
                })
            }
        })
    }
}

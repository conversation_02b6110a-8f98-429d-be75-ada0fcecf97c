package com.decurret_dcp.dcjpy.bcclient

import com.decurret_dcp.dcjpy.bcclient.adaptor.AwsSqsAdaptor
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractInfo
import com.decurret_dcp.dcjpy.bcclient.base.properties.S3Property

import com.decurret_dcp.dcjpy.bcclient.base.adaptor.AwsS3Adaptor
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool
import com.decurret_dcp.dcjpy.bcclient.base.websocket.SubWebSocketConnectionPool
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ActiveProfiles
import software.amazon.awssdk.services.sqs.SqsClient
import spock.lang.Specification

import java.util.function.Predicate
import java.util.stream.Collectors
import java.util.stream.Stream

@ActiveProfiles("test")
@SpringBootTest(
        classes = [ContractInfoConfig.class]
)
// TODO bcclient-base モジュールだと ApplicationProperties, LocalProperties をインジェクトできないため、やむえずモジュール移動
abstract class IntegrationTestBase extends Specification {

    @MockBean
    protected MainWebSocketConnectionPool mainWebSocketConnectionPool

    @MockBean
    protected SubWebSocketConnectionPool subWebSocketConnectionPool

    @Autowired
    protected ContractInfo contractInfo

    @Autowired
    protected AwsS3Adaptor awsS3Adaptor

    @Autowired
    protected AwsSqsAdaptor awsSqsAdaptor


    @TestConfiguration
    static class ContractInfoConfig {

        @Bean
        AwsSqsAdaptor awsSqsAdaptor(SqsClient sqsClient) {
            return new AwsSqsAdaptor(sqsClient, "dummy") {
                public void send(String requestId) {
                    // do nothing.
                }
            }
        }

        @Bean
        AwsS3Adaptor awsS3Adaptor(S3Property s3Property) {
            ClassLoader classLoader = getClass().getClassLoader()
            final Map<String, byte[]> abiContents = Stream.of(
                    "Provider.json", "Validator.json", "TupleTypes.json", "Token.json", "Test.json")
                    .collect(Collectors.toMap(
                            fileName -> "3000/" + fileName,
                            fileName -> new File(classLoader.getResource(fileName as String).getFile()).getBytes()
                    ))

            return new AwsS3Adaptor(s3Property) {
                public Map<String, byte[]> fetchAbiContents(String bucketName, Predicate<String> filterCondition) {
                    return abiContents;
                }
            }
        }
    }
}
package com.decurret_dcp.dcjpy.bcclient.controller.get

import com.decurret_dcp.dcjpy.bcclient.BCClientMain
import com.decurret_dcp.dcjpy.bcclient.adaptor.AwsSqsAdaptor
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractInfo
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool
import com.decurret_dcp.dcjpy.bcclient.base.websocket.SubWebSocketConnectionPool
import com.decurret_dcp.dcjpy.bcclient.controller.BCClientController
import com.decurret_dcp.dcjpy.bcclient.service.FindService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import spock.lang.Specification

@ActiveProfiles("test")
@WebMvcTest(BCClientController.class)
@ComponentScan(basePackages = "com.decurret_dcp.dcjpy.bcclient")
class GetControllerSpec extends Specification{

    @MockBean
    private BCClientMain application

    @MockBean
    private FindService findService

    @MockBean
    private ContractInfo contractInfo

    @MockBean
    private AwsSqsAdaptor awsSqsAdaptor

    @MockBean
    private MainWebSocketConnectionPool mainWebSocketConnectionPool

    @MockBean
    private SubWebSocketConnectionPool subWebSocketConnectionPool

    @Autowired
    private MockMvc mockMvc

    def "Get: return BadRequest when requestId is greater than maximum"() {
        when:
        def requestId = "r" * 129

        then:
        mockMvc.perform(
                MockMvcRequestBuilders.get("/get/{requestId}", requestId)
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
        ).andExpect(MockMvcResultMatchers.status().isBadRequest())
    }
}

package com.decurret_dcp.dcjpy.bcclient

import com.decurret_dcp.dcjpy.bcclient.adaptor.AwsSqsAdaptor
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractAbi
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractFunction
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractInfo
import com.decurret_dcp.dcjpy.bcclient.base.service.ExternalAbiResolver
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId
import com.decurret_dcp.dcjpy.bcclient.service.CallService

import com.decurret_dcp.dcjpy.bcclient.service.param.CallResult
import com.decurret_dcp.dcjpy.bcclient.base.exception.BadRequestException
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand

import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthCallExecutor
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.SpyBean

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.*

// TODO bcclient-base モジュールだと ApplicationProperties, LocalProperties をインジェクトできないため、やむえずモジュール移動
class CallServiceSpec extends IntegrationTestBase {

    @Autowired
    private CallService callService

    @SpyBean
    private AwsSqsAdaptor awsSqsAdaptor;

    @SpyBean
    private ExternalAbiResolver externalAbiResolver

    @SpyBean
    private EthCallExecutor ethCallExecutor

    @SpyBean
    private ContractInfo contractInfo

    def "Execute: CallServiceが正常に実行できること"() {
        when:
        doCallRealMethod()
                .when(this.contractInfo).hasContractApi((ZoneId) notNull(), (String) notNull())

        doCallRealMethod()
                .when(this.contractInfo).getContractAbi((ZoneId) notNull(), (String) notNull())

        doReturn(Map.of())
                .when(this.ethCallExecutor).callTransaction(notNull(), notNull())

        TransactionCommand input = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        CallResult callServiceOutput = this.callService.execute(input)

        then:
        callServiceOutput.getHashMapData() != null

        where:
        contractName | method           | args
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
                put("chkEnabled", true)
            }
        }
    }

    def "Execute: コントラクト名が存在しない場合にBadRequestExceptionが発生すること"() {
        when:
        doReturn(new ContractAbi("name", "0x00", new ArrayList<ContractFunction>()))
                .when(this.externalAbiResolver).resolve((ZoneId) notNull(), (String) notNull())

        TransactionCommand input = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        this.callService.execute(input)

        then:
        thrown(ex)

        where:
        contractName | method           | args                          | ex
        "none"       | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }                                                               | BadRequestException
    }

    def "Execute: メソッド名が存在しない場合にBadRequestExceptionが発生すること"() {
        when:
        doReturn(new ContractAbi("name", "0x00", new ArrayList<ContractFunction>()))
                .when(this.externalAbiResolver).resolve((ZoneId) notNull(), (String) notNull())

        TransactionCommand input = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        this.callService.execute(input)

        then:
        thrown(ex)

        where:
        contractName | method | args                          | ex
        "Validator"  | "none" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
                put("chkEnabled", true)
            }
        }                                                     | BadRequestException
    }

    def "Execute: パラメータが不足している場合にContractNotFoundExceptionが発生すること"() {
        when:
        doCallRealMethod()
                .when(this.contractInfo).hasContractApi((ZoneId) notNull(), (String) notNull())

        doCallRealMethod()
                .when(this.contractInfo).getContractAbi((ZoneId) notNull(), (String) notNull())

        TransactionCommand input = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        this.callService.execute(input)

        then:
        thrown(ex)

        where:
        contractName | method           | args                          | ex
        "Validator"  | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 13107)
            }
        }                                                               | BadRequestException
    }
}

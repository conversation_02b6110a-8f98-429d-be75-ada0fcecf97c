package com.decurret_dcp.dcjpy.bcclient

import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand
import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthCallExecutor

import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractAbi
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractFunction
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId
import org.springframework.beans.factory.annotation.Autowired

// TODO bcclient-base モジュールだと ApplicationProperties, LocalProperties をインジェクトできないため、やむえずモジュール移動
class EthCallExecutorSpec extends IntegrationTestBase {

    @Autowired
    private EthCallExecutor ethCallExecutor

    def "setup"() {
    }

    def "DecodeCallResult: 配列のuint256型、bytes32型、bool型がデコードできること"() {
        when:
        TransactionCommand input = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        ContractAbi contractAbi = this.contractInfo.getContractAbi(input.zoneId, input.getContractName())
        ContractFunction contractFunction = contractAbi.getFunction(input.getMethod())
        Map<String, Object> result = this.ethCallExecutor.decodeCallResult(
                "0x0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001800000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000033078313030000000000000000000000000000000000000000000000000000000307831303100000000000000000000000000000000000000000000000000000030783130320000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001",
                contractFunction
        )
        List<String> someUint = result.get("someUint") as List<String>
        List<String> someByte = result.get("someByte") as List<String>
        List<Boolean> someBool = result.get("someBool") as List<Boolean>
        String count = result.get("count")

        then:
        count == "3"
        someUint.size() == 3
        someByte.size() == 3
        someBool.size() == 3

        someUint.get(0) == "1"
        someUint.get(1) == "2"
        someUint.get(2) == "3"

        someByte.get(0) == "0x3078313030000000000000000000000000000000000000000000000000000000"
        someByte.get(1) == "0x3078313031000000000000000000000000000000000000000000000000000000"
        someByte.get(2) == "0x3078313032000000000000000000000000000000000000000000000000000000"

        someBool.get(0) == true
        someBool.get(1) == false
        someBool.get(2) == true

        where:
        contractName | method     | args
        "Test"       | "getArray" | new LinkedHashMap<String, Object>()
    }
}

package com.decurret_dcp.dcjpy.bcclient.controller


import com.decurret_dcp.dcjpy.bcclient.BCClientMain
import com.decurret_dcp.dcjpy.bcclient.IntegrationTestBase
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractInfo
import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool
import com.decurret_dcp.dcjpy.bcclient.base.websocket.SubWebSocketConnectionPool
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

import static org.mockito.Mockito.when

@ActiveProfiles("test")
@AutoConfigureMockMvc
class CustomHealthIndicatorSpec extends IntegrationTestBase {

    @MockBean
    private BCClientMain application
    @MockBean
    private ApplicationProperty applicationProperties
    @MockBean
    private ContractInfo contractInfo
    @MockBean
    private MainWebSocketConnectionPool mainWebSocketConnectionPool
    @MockBean
    private SubWebSocketConnectionPool subWebSocketConnectionPool
    @Autowired
    private MockMvc mockMvc

    def "Health: WebSocketに接続されている場合にステータス200が返ってくること"() {
        when:
        when(applicationProperties.isUseSubWebSocket())
                .thenReturn(true)
        when(mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(false)
        when(subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(false)

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.get("/actuator/health")
        ).andExpect(MockMvcResultMatchers.status().isOk())
    }

    def "Health: MainWebSocketに接続されていない場合にステータス503が返ってくること"() {
        when:
        when(applicationProperties.isUseSubWebSocket())
                .thenReturn(true)
        when(mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(true)
        when(subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(false)

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.get("/actuator/health")
        ).andExpect(MockMvcResultMatchers.status().isServiceUnavailable())
    }

    def "Health: SubWebSocketに接続されていない場合にステータス503が返ってくること"() {
        when:
        when(applicationProperties.isUseSubWebSocket())
                .thenReturn(true)
        when(mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(false)
        when(subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(true)

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.get("/actuator/health")
        ).andExpect(MockMvcResultMatchers.status().isServiceUnavailable())
    }

    def "Health: SubWebSocketを使用しないときMainWebSocketが接続されている場合にステータス200が返ってくること"() {
        when:
        when(applicationProperties.isUseSubWebSocket())
                .thenReturn(false)
        when(mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(false)
        when(subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(true)

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.get("/actuator/health")
        ).andExpect(MockMvcResultMatchers.status().isOk())
    }

    def "Health: SubWebSocketを使用しないときMainWebSocketが接続されていない場合にステータス503が返ってくること"() {
        when:
        when(applicationProperties.isUseSubWebSocket())
                .thenReturn(false)
        when(mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(true)
        when(subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed())
                .thenReturn(true)

        then:
        this.mockMvc.perform(
                MockMvcRequestBuilders.get("/actuator/health")
        ).andExpect(MockMvcResultMatchers.status().isServiceUnavailable())
    }
}

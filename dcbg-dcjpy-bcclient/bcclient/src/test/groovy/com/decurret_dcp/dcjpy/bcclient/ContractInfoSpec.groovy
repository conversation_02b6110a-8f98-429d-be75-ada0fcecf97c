package com.decurret_dcp.dcjpy.bcclient

import com.decurret_dcp.dcjpy.bcclient.base.exception.ContractNotFoundException
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId

// TODO bcclient-base モジュールだと ApplicationProperties, LocalProperties をインジェクトできないため、やむえずモジュール移動
class ContractInfoSpec extends IntegrationTestBase {

    def "Convert: コントラクト名が存在しない場合にContractNotFoundExceptionが発生すること"() {
        when:
        TransactionCommand input = TransactionCommand.builder()
                .zoneId(ZoneId.of("3000"))
                .contractName(contractName)
                .method(method)
                .args(args)
                .build()
        this.contractInfo.getContractAbi(input.zoneId, input.getContractName())

        then:
        thrown(ex)

        where:
        contractName | method           | args | ex
        "none"       | "hasValidatorID" | new LinkedHashMap<String, Object>() {
            {
                put("validID", 123)
                put("chkEnabled", true)
            }
        }                                      | ContractNotFoundException
    }
}

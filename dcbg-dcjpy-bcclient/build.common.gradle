
group = 'com.decurret_dcp'
version = '0.0.1-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {
    // spring
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-json'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    implementation "org.springframework.boot:spring-boot-configuration-processor"
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // jaxb
    implementation 'javax.xml.bind:jaxb-api:2.3.1'

    // apache codec
    implementation group: 'commons-codec', name: 'commons-codec', version: '1.9'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // web3j
    implementation 'org.web3j:core:4.9.7'
    implementation "com.squareup.okhttp3:okhttp:4.9.0" // https://github.com/web3j/web3j/issues/1271

    // for aws
    implementation platform('software.amazon.awssdk:bom:2.16.78')
    implementation 'software.amazon.awssdk:s3'
    implementation 'software.amazon.awssdk:sts'
    implementation 'software.amazon.awssdk:sqs'
    implementation 'software.amazon.awssdk:dynamodb'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    // spock
    testImplementation 'org.codehaus.groovy:groovy:3.0.10'
    testImplementation platform("org.spockframework:spock-bom:2.1-groovy-3.0")
    testImplementation 'org.spockframework:spock-core:2.1-groovy-3.0'
    testImplementation 'org.spockframework:spock-spring:2.1-groovy-3.0'
    testImplementation 'com.athaydes:spock-reports:2.3.0-groovy-3.0'

    // adhoc
    testImplementation 'org.testcontainers:testcontainers:1.17.6'
    testImplementation 'org.testcontainers:spock:1.17.6'

    // s3 mock
    testImplementation 'io.findify:s3mock_2.12:0.2.6'
    // sqs
    testImplementation 'org.elasticmq:elasticmq-server_2.12:0.14.15'
    // mockito
    testImplementation 'org.mockito:mockito-inline:3.6.28'
}

tasks.named('test') {
    useJUnitPlatform()
    testLogging {
        events "FAILED", "SKIPPED"
        displayGranularity = 4
    }
    maxHeapSize = '2G'
}

jacoco {
    toolVersion = "0.8.7"
}

jacocoTestReport {
    reports {
        xml.required = false
        csv.required = false
        html.outputLocation = layout.buildDirectory.dir("jacocoHtml")
    }
}

test.finalizedBy jacocoTestReport

eclipse {
    classpath {
        downloadJavadoc = true
        downloadSources = true
    }
}

idea {
    module {
        downloadJavadoc = true
        downloadSources = true
    }
}

version: "3.8"
services:
  #  app:
  #    image: amazoncorretto:17-alpine-jdk
  #    ports:
  #      - "8081:8081"
  #    environment:
  #      - SPRING_PROFILES_ACTIVE=local
  #    working_dir: /app
  #    volumes:
  #      - .:/app
  #    entrypoint: bash
  #    command: ./gradlew bootRun

  minio:
    image: minio/minio:RELEASE.2021-04-06T23-11-00Z.hotfix.f3cd60697
    ports:
      - "9000:9000"
    environment:
      - MINIO_ACCESS_KEY=access123
      - MINIO_SECRET_KEY=secret123
    entrypoint: sh
    command: -c "
      rm -rf /data/*;
      mkdir -p /data/.minio.sys/buckets;
      cp -r /policies/* /data/.minio.sys/;
      cp -r /export/* /data/;
      /usr/bin/minio server /data;
      "
    volumes:
      - ./docker/minio/data:/data
      - ./docker/minio/export:/export
      - ./docker/minio/config:/root/.minio
      - ./docker/minio/policies:/policies

  minio-external:
    image: minio/minio:RELEASE.2021-04-06T23-11-00Z.hotfix.f3cd60697
    ports:
      - "9001:9000"
    environment:
      - MINIO_ACCESS_KEY=access123
      - MINIO_SECRET_KEY=secret123
    entrypoint: sh
    command: -c "
      rm -rf /data/*;
      mkdir -p /data/.minio.sys/buckets;
      cp -r /policies/* /data/.minio.sys/;
      cp -r /export/* /data/;
      /usr/bin/minio server /data;
      "
    volumes:
      - ./docker/minio-external/data:/data
      - ./docker/minio-external/export:/export
      - ./docker/minio-external/config:/root/.minio
      - ./docker/minio-external/policies:/policies

  localstack:
    image: localstack/localstack:3.0.2
    environment:
      - SERVICES=sqs,dynamodb,s3
    ports:
      - "4566-4599:4566-4599"
    volumes:
      - ./docker/localstack/init-localstack.sh:/etc/localstack/init/ready.d/init-aws.sh

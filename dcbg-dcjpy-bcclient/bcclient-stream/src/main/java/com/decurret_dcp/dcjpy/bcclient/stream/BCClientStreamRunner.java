package com.decurret_dcp.dcjpy.bcclient.stream;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bcclient.stream.application.SendTransactionApplication;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class BCClientStreamRunner implements ApplicationRunner {

    private final SendTransactionApplication application;

    @Override
    public void run(ApplicationArguments args) {
        this.application.execute();
    }
}

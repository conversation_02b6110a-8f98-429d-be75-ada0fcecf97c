package com.decurret_dcp.dcjpy.bcclient.stream.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.decurret_dcp.dcjpy.bcclient.base.controller.CustomHealthIndicator;
import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty;
import com.decurret_dcp.dcjpy.bcclient.base.transaction.NonceManagerHolder;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool;
import com.decurret_dcp.dcjpy.bcclient.stream.application.BcEventListenApplication;

@Configuration
public class BCClientStreamConfig {

    @Bean
    public MainWebSocketConnectionPool mainWebSocketConnectionPool(
            ApplicationProperty property, BcEventListenApplication listenApplication
    ) {
        return new MainWebSocketConnectionPool(property, listenApplication);
    }

    @Bean
    public NonceManagerHolder nonceManagerHolder(MainWebSocketConnectionPool connectionPool) {
        return NonceManagerHolder.create(connectionPool);
    }

    @Bean
    public CustomHealthIndicator customHealthIndicator(
            ApplicationProperty property, MainWebSocketConnectionPool connectionPool
    ) {
        return new CustomHealthIndicator(property, connectionPool);
    }
}

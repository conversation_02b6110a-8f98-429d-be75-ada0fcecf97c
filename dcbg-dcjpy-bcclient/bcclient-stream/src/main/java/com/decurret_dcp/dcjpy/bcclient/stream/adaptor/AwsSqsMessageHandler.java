package com.decurret_dcp.dcjpy.bcclient.stream.adaptor;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.LockSupport;

import javax.annotation.PostConstruct;

import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bcclient.base.properties.SqsProperty;
import com.decurret_dcp.dcjpy.bcclient.stream.domain.ComponentStatus;
import com.decurret_dcp.dcjpy.bcclient.stream.domain.RequestIdMessage;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest;
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest;
import software.amazon.awssdk.services.sqs.model.GetQueueUrlResponse;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageResponse;
import software.amazon.awssdk.services.sqs.model.SqsException;

@Component
@Slf4j
public class AwsSqsMessageHandler {

    private static final ScheduledExecutorService SCHEDULED_SERVICE = Executors.newSingleThreadScheduledExecutor();

    private final ComponentStatus status;

    private final SqsClient sqsClient;

    private final SqsProperty property;

    private final String queueUrl;

    private final LinkedBlockingQueue<Message> messageQueue;

    private final AtomicInteger failureCount;

    public AwsSqsMessageHandler(SqsClient sqsClient, SqsProperty property) {
        this.status = new ComponentStatus();
        this.sqsClient = sqsClient;
        this.property = property;
        this.queueUrl = initQueueUrl(sqsClient, property.queueName);
        this.messageQueue = new LinkedBlockingQueue<>();
        this.failureCount = new AtomicInteger(0);
    }

    private static String initQueueUrl(SqsClient sqsClient, String queueName) {
        GetQueueUrlRequest requestUrl = GetQueueUrlRequest.builder()
                .queueName(queueName)
                .build();
        GetQueueUrlResponse responseUrl = sqsClient.getQueueUrl(requestUrl);

        return responseUrl.queueUrl();
    }

    @PostConstruct
    public void initialize() {
        SCHEDULED_SERVICE.schedule(() -> this.decrementCounter(), 10L, TimeUnit.MINUTES);
    }

    // 失敗回数を蓄積するだけだと、何かしらの理由で稀に発生する事象が積み重なって閾値を突破する可能性があるため、
    // 一定時間ごとに失敗回数をデクリメントする。
    private void decrementCounter() {
        for (int index = 0; index < 5; index++) {
            int currentCount = this.failureCount.get();
            if (currentCount == 0) {
                return;
            }

            if (currentCount < 0) {
                if (this.failureCount.compareAndSet(currentCount, 0)) {
                    return;
                }

                LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(100L));
                continue;
            }

            if (this.failureCount.compareAndSet(currentCount, currentCount - 1)) {
                return;
            }

            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(100L));
        }

        log.warn("Failed to decrement failed counter. failureCount : {}", Integer.valueOf(this.failureCount.get()));
    }

    public boolean isActive() {
        return this.status.isRunning();
    }

    public RequestIdMessage poll() {
        Message message = this.messageQueue.poll();
        if (message != null) {
            return this.toRequestId(message);
        }

        // 既に停止要求を受け取っている場合は、追加で SQS からメッセージを取得しない。
        if (this.status.isRunning() == false) {
            this.status.terminated();
            return null;
        }

        List<Message> messages = this.fetchMessages();
        if (messages.isEmpty()) {
            return null;
        }

        for (Message newMessage : messages) {
            boolean offered = this.messageQueue.offer(newMessage);
            if (offered == false) {
                // キューから取得できないことを確認のうえで追加しているので、発生しない想定
                log.error("Failed to queuing message. {}", newMessage);
                break;
            }
        }

        return this.poll();
    }

    public RequestIdMessage toRequestId(Message message) {
        return RequestIdMessage.builder()
                .message(message)
                .requestId(message.body())
                .build();
    }

    private List<Message> fetchMessages() {
        ReceiveMessageRequest request = ReceiveMessageRequest.builder()
                .queueUrl(this.queueUrl)
                .visibilityTimeout(this.property.visibilityTimeout)
                .waitTimeSeconds(this.property.waitTimeSeconds)
                .build();

        List<Message> messages = null;
        try {
            ReceiveMessageResponse response = this.sqsClient.receiveMessage(request);
            messages = response.messages();
        } catch (SqsException sqsExc) {
            int failedCount = this.failureCount.incrementAndGet();
            if (failedCount >= 5) {
                throw new RuntimeException(
                        "Exceeded the limit count of failure to receive message from " + this.queueUrl, sqsExc
                );
            }

            log.error("Failed to receive message from {}, failureCount : {}",
                      this.queueUrl, Integer.valueOf(failedCount), sqsExc);
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(700L));
        }

        return (messages != null) ? messages : List.of();
    }

    public void complete(RequestIdMessage requestIdMessage) {
        Message message = requestIdMessage.message;

        DeleteMessageRequest request = DeleteMessageRequest.builder()
                .queueUrl(this.queueUrl)
                .receiptHandle(message.receiptHandle())
                .build();

        try {
            this.sqsClient.deleteMessage(request);
        } catch (SqsException sqsExc) {
            log.error("Failed to delete message from {}. message : {}", this.queueUrl, message, sqsExc);
        }
    }

    @EventListener
    public void onCloseEvent(ContextClosedEvent event) {
        if (this.status.shutdown()) {
            log.warn("Ordered to terminating polling message from sqs : {}, remaining messages : {}, "
                             + "terminate event : {}",
                     this.property.queueName, Integer.valueOf(this.messageQueue.size()), event);

            SCHEDULED_SERVICE.shutdown();
        }
    }
}

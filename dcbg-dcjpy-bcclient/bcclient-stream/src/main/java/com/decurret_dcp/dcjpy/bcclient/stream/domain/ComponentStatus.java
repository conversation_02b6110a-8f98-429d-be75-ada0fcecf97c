package com.decurret_dcp.dcjpy.bcclient.stream.domain;

import java.util.concurrent.atomic.AtomicReference;

public class ComponentStatus {

    private final AtomicReference<StatusValue> ref;

    public ComponentStatus() {
        this.ref = new AtomicReference<>(StatusValue.RUNNING);
    }

    public boolean shutdown() {
        return this.ref.compareAndSet(StatusValue.RUNNING, StatusValue.IN_TERMINATING);
    }

    public boolean terminated() {
        return this.ref.compareAndSet(StatusValue.IN_TERMINATING, StatusValue.TERMINATED);
    }

    public boolean isRunning() {
        return (this.ref.get() == StatusValue.RUNNING);
    }

    public boolean isTerminated() {
        return (this.ref.get() == StatusValue.TERMINATED);
    }

    private static enum StatusValue {

        RUNNING,

        IN_TERMINATING,

        TERMINATED;
    }
}

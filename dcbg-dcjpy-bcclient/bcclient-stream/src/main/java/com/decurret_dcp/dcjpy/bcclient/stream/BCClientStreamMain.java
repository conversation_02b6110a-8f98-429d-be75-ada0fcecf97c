package com.decurret_dcp.dcjpy.bcclient.stream;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.decurret_dcp.dcjpy.bcclient.base.properties.DynamoDbProperty;
import com.decurret_dcp.dcjpy.bcclient.base.properties.S3Property;
import com.decurret_dcp.dcjpy.bcclient.base.properties.SqsProperty;

@EnableScheduling
@SpringBootApplication
@ComponentScan("com.decurret_dcp.dcjpy.bcclient")
@EnableConfigurationProperties({ S3Property.class, SqsProperty.class, DynamoDbProperty.class })
public class BCClientStreamMain {

    public static void main(String[] args) {
        SpringApplication.run(BCClientStreamMain.class, args);
    }
}

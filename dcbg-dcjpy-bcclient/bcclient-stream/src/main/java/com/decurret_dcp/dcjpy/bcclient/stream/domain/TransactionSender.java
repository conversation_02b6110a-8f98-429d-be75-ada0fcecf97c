package com.decurret_dcp.dcjpy.bcclient.stream.domain;

import java.io.IOException;
import java.math.BigInteger;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

import org.springframework.stereotype.Component;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.Hash;
import org.web3j.crypto.RawTransaction;
import org.web3j.crypto.TransactionEncoder;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.Response;
import org.web3j.protocol.core.methods.response.EthSendTransaction;
import org.web3j.protocol.core.methods.response.EthTransaction;
import org.web3j.protocol.core.methods.response.Transaction;
import org.web3j.utils.Numeric;

import com.decurret_dcp.dcjpy.bcclient.base.adaptor.AwsDynamoDbAdaptor;
import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty;
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand;
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionResolver;
import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthTransactionCommand;
import com.decurret_dcp.dcjpy.bcclient.base.transaction.NonceManager;
import com.decurret_dcp.dcjpy.bcclient.base.transaction.NonceManagerHolder;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionStatus;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionValue;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class TransactionSender {

    private static final BigInteger GAS_PRICE = BigInteger.valueOf(0);

    private final TransactionResolver transactionResolver;

    private final NonceManagerHolder nonceManagerHolder;

    private final MainWebSocketConnectionPool connectionPool;

    private final AwsDynamoDbAdaptor dynamoDbAdaptor;

    private final BigInteger gasLimit;

    public TransactionSender(
            ApplicationProperty property, TransactionResolver transactionResolver,
            NonceManagerHolder nonceManagerHolder, MainWebSocketConnectionPool connectionPool,
            AwsDynamoDbAdaptor dynamoDbAdaptor
    ) {
        this.transactionResolver = transactionResolver;
        this.nonceManagerHolder = nonceManagerHolder;
        this.connectionPool = connectionPool;
        this.dynamoDbAdaptor = dynamoDbAdaptor;
        this.gasLimit = BigInteger.valueOf(property.getGasLimit());
    }

    public boolean handleTransaction(String requestId, TransactionValue transaction) {
        // 状態が処理受付中でない場合は、該当リクエストは処理受付済みとみなす。
        if (transaction.transactionStatus != TransactionStatus.QUEUING) {
            log.warn("Unexpected transaction status. requestId : {}, status : {}, contractName : {}, method : {}",
                     requestId, transaction.transactionStatus.getValue(), transaction.contractName, transaction.method);

            return false;
        }

        TransactionCommand command = TransactionCommand.create(transaction);
        EthTransactionCommand ethCommand = this.transactionResolver.resolve(command);

        // 念のため、値の検証を実施
        ethCommand.verify();

        return this.doSendTransaction(requestId, ethCommand, TransactionStatus.QUEUING);
    }

    boolean doSendTransaction(
            String requestId, EthTransactionCommand ethCommand, TransactionStatus beforeStatus
    ) {
        NonceManager nonceManager = this.nonceManagerHolder.getNonceManager();
        String nonceKey = nonceManager.getNonceKey();
        BigInteger nonce = nonceManager.getNonce();

        // 決定した nonce 値を先に DynamoDB に保存する
        this.dynamoDbAdaptor.prepareSending(requestId, nonceKey, nonce, beforeStatus);
        log.debug("Prepared transaction with nonce = {}, requestId = {}", nonce, requestId);

        String rawTransaction = this.initSignedRawTransaction(nonceManager, ethCommand, nonce);

        // 送信件数を1件増分
        this.dynamoDbAdaptor.incrementSendingCounter();

        Web3j web3j = this.connectionPool.getWebSocketConnection();

        EthSendTransaction result = null;
        String transactionHash = null;
        try {
            result = web3j.ethSendRawTransaction(rawTransaction).send();
        } catch (IOException | RuntimeException exc) {
            transactionHash = Hash.sha3(rawTransaction);

            // IOException が発生した場合はネットワークが切断された可能性があるので、
            // 送信した send トランザクションが画定しているか確認する必要がある。
            log.error("Exception occurred while sending transaction to DLT. "
                              + "Refreshing DLT connection and checking the transaction is commited. "
                              + "requestId = {}, transactionHash = {}, nonce = {}, errorMessage = {}, rawTransaction = {}",
                      requestId, transactionHash, nonce, exc.getMessage(), rawTransaction, exc);

            this.connectionPool.createWebSocketConnection();
            web3j = this.connectionPool.getWebSocketConnection();

            boolean checkResult = this.doCheckTransaction(web3j, requestId, transactionHash);
            if (checkResult == false) {
                log.error("[MUST DO RECOVERY ACTION] Failed to check transaction commited. "
                                  + "requestId = {}, transactionHash = {}, nonce = {}, rawTransaction = {}, nonceKey = {}",
                          requestId, transactionHash, nonce, rawTransaction, nonceKey);

                this.dynamoDbAdaptor.decrementSendingCounter();
                return false;
            }

            log.info("Succeed to send transaction to DLT before exception occurred. "
                             + "requestId = {}, nonce = {}, transactionHash = {}",
                     requestId, nonce, transactionHash);
        }

        // DLT よりエラーが返却された場合、DLT に Send トランザクションは処理対象として取り込まれていない。
        if ((result != null) && result.hasError()) {
            Response.Error error = result.getError();
            log.error("Failed to send transaction to DLT. requestId = {}, nonce = {}, "
                              + "errorCode = {}, errorMessage = {}, rawTransaction = {}, nonceKey = {}",
                      requestId, nonce, error.getCode(), error.getMessage(), rawTransaction, nonceKey);

            this.dynamoDbAdaptor.decrementSendingCounter();

            // 鍵生成後の再送処理でもエラーが発生した場合は、リトライを行わない。運用で対応をする。
            if (beforeStatus != TransactionStatus.QUEUING) {
                log.error("[MUST DO RECOVERY ACTION] Failed to retry sending transaction to DLT. "
                                  + "requestId = {}, nonce = {}, rawTransaction = {}, nonceKey = {}",
                          requestId, nonce, rawTransaction, nonceKey);

                return false;
            }

            log.warn("Retrying to send transaction to DTL with refreshing nonceKey. requestId = {}", requestId);

            // 鍵を生成し直して Send トランザクションを再送信する
            return this.retrySendTransaction(requestId, ethCommand);
        }

        if (result != null) {
            transactionHash = result.getTransactionHash();

            log.info("Succeed to send transaction to DLT. requestId = {}, nonce = {}, transactionHash = {}",
                     requestId, nonce, transactionHash);
        }

        try {
            this.dynamoDbAdaptor.recordTransactionHash(requestId, transactionHash);
        } catch (RuntimeException exc) {
            log.error("Failed to record sent status. requestId = {}, transactionHash = {}",
                      requestId, transactionHash, exc);
        }

        return true;
    }

    private String initSignedRawTransaction(
            NonceManager nonceManager, EthTransactionCommand ethCommand, BigInteger nonce
    ) {
        Credentials credentials = nonceManager.getCredentials();

        String contractAddress = ethCommand.contractAddress();
        String encodedFunction = ethCommand.encode();

        RawTransaction rawTransaction =
                RawTransaction.createTransaction(nonce, GAS_PRICE, this.gasLimit, contractAddress, encodedFunction);

        // sign transaction
        byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction, credentials);

        return Numeric.toHexString(signedMessage);
    }

    private boolean doCheckTransaction(Web3j web3j, String requestId, String transactionHash) {
        for (int retryCount = 0; retryCount < 3; retryCount++) {
            // ブロック確定されるまでの時間待つ
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(2500L + 2000L * retryCount));

            EthTransaction ethTransaction;
            try {
                ethTransaction = web3j.ethGetTransactionByHash(transactionHash).send();
            } catch (IOException fetchExc) {
                log.warn("Failed to fetch transaction from DLT. Refreshing DLT connection. "
                                 + "requestId = {}, transactionHash = {}, errorMessage = {}",
                         requestId, transactionHash, fetchExc.getMessage());

                this.connectionPool.createWebSocketConnection();
                continue;
            } catch (RuntimeException cause) {
                log.warn("Unexpected error occurred in fetching transaction from DLT. Refreshing DLT connection. "
                                 + "requestId = {}, transactionHash = {}, errorMessage = {}",
                         requestId, transactionHash, cause.getMessage(), cause);

                this.connectionPool.createWebSocketConnection();
                continue;
            }

            if (ethTransaction.hasError()) {
                Response.Error error = ethTransaction.getError();
                log.warn("Failed to fetch transaction from DLT. requestId = {}, transactionHash = {}, "
                                 + "errorCode = {}, errorMessage = {}",
                         requestId, transactionHash, error.getCode(), error.getMessage());

                continue;
            }

            Optional<Transaction> optional = ethTransaction.getTransaction();
            if (optional.isPresent()) {
                return true;
            }
        }

        return false;
    }

    private boolean retrySendTransaction(String requestId, EthTransactionCommand ethCommand) {
        this.nonceManagerHolder.refreshCredential();

        return this.doSendTransaction(requestId, ethCommand, TransactionStatus.BEFORE_SENT);
    }
}

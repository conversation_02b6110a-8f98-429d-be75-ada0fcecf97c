package com.decurret_dcp.dcjpy.bcclient.stream.application;

import java.io.IOException;
import java.util.List;

import org.springframework.stereotype.Component;
import org.web3j.abi.DefaultFunctionReturnDecoder;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.methods.response.EthBlock;
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt;
import org.web3j.protocol.core.methods.response.TransactionReceipt;

import com.decurret_dcp.dcjpy.bcclient.base.adaptor.AwsDynamoDbAdaptor;
import com.decurret_dcp.dcjpy.bcclient.base.exception.BlockchainIOException;
import com.decurret_dcp.dcjpy.bcclient.base.util.EthDataTypeUtil;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionStatus;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.BcEventHandler;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.WebSocketConnectionPoolBase;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Component
@Slf4j
public class BcEventListenApplication implements BcEventHandler {

    private static final String ERROR_METHOD_ID = "0x08c379a0";

    private static final String NO_REVERT_REASON = "0x";

    private final AwsDynamoDbAdaptor dynamoDbAdaptor;

    @Override
    public void onEvent(WebSocketConnectionPoolBase connectionPool, EthBlock ethBlock) {
        Web3j web3j = connectionPool.getWebSocketConnection();

        List<EthBlock.TransactionResult> results = ethBlock.getResult().getTransactions();
        for (EthBlock.TransactionResult result : results) {
            this.handleTransaction(web3j, (EthBlock.TransactionObject) result);
        }
    }

    void handleTransaction(Web3j web3j, EthBlock.TransactionObject transactionObj) {
        String transactionHash = transactionObj.getHash();

        TransactionReceipt receipt;
        try {
            EthGetTransactionReceipt ethReceipt = web3j.ethGetTransactionReceipt(transactionHash).send();
            receipt = ethReceipt.getResult();
        } catch (IOException ioExc) {
            log.error("GetTransactionReceipt failed.", ioExc);
            throw new BlockchainIOException("ethGetTransactionReceipt.send() failed", ioExc);
        }

        TransactionStatus status = receipt.isStatusOK() ? TransactionStatus.COMPLETED : TransactionStatus.REVERTED;
        String revertReason = null;

        // BCClientStreamがSendしたトランザクションであるか判定する
        boolean queuedTransaction = this.dynamoDbAdaptor.existsTransactionWithHash(transactionHash);

        if (status == TransactionStatus.REVERTED) {
            revertReason = this.doRecordRevertReason(transactionHash, receipt);
            if (queuedTransaction == true) {
                log.error("Reverted send transaction. transactionHash : {}, reason : {}",
                          transactionHash, revertReason);
            } else {
                log.warn("A transaction that was not sent by BCClientStream was reverted. " +
                                 "transactionHash : {}, reason : {}", transactionHash, revertReason);
            }
        }

        boolean recorded = this.dynamoDbAdaptor.completeTransactionStatus(transactionHash, status, revertReason);
        if (recorded == false) {
            return;
        }

        if (queuedTransaction == true) {
            this.dynamoDbAdaptor.decrementSendingCounter();
        }
    }

    @SuppressWarnings("unchecked")
    private String doRecordRevertReason(String transactionHash, TransactionReceipt receipt) {
        String reason = receipt.getRevertReason();
        if (NO_REVERT_REASON.equals(reason)) {
            return reason;
        }

        try {
            DefaultFunctionReturnDecoder decoder = new DefaultFunctionReturnDecoder();
            List<Type> decodedValues = decoder.decodeFunctionResult(
                    reason.substring(ERROR_METHOD_ID.length()),
                    List.of(EthDataTypeUtil.toTypeReference("string"))
            );
            Utf8String revertReason = (Utf8String) decodedValues.get(0);

            return revertReason.getValue();
        } catch (RuntimeException exc) {
            log.warn("Failed to decode revertReason. transactionHash : {}", transactionHash, exc);

            return reason;
        }
    }
}

package com.decurret_dcp.dcjpy.bcclient.stream.application;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.decurret_dcp.dcjpy.bcclient.base.adaptor.AwsDynamoDbAdaptor;
import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionValue;
import com.decurret_dcp.dcjpy.bcclient.stream.adaptor.AwsSqsMessageHandler;
import com.decurret_dcp.dcjpy.bcclient.stream.domain.RequestIdMessage;
import com.decurret_dcp.dcjpy.bcclient.stream.domain.TransactionSender;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class SendTransactionApplication {

    private final ApplicationProperty property;

    private final TransactionSender transactionSender;

    private final AwsSqsMessageHandler messageHandler;

    private final AwsDynamoDbAdaptor dynamoDbAdaptor;

    private long lastLoggedAt = 0L;

    public void execute() {
        while (true) {
            // 同時送信件数の確認
            boolean checkCounter = this.checkSendingCount();
            if (checkCounter == false) {
                LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(300L));
                continue;
            }

            RequestIdMessage message = this.messageHandler.poll();
            if (message == null) {
                // イベントが取得できず、ハンドラが稼働していない場合は処理を終了する
                if (this.messageHandler.isActive() == false) {
                    break;
                }

                LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(1000L));
                continue;
            }

            boolean isCompleted = false;
            try {
                isCompleted = this.handleTransaction(message.requestId);
            } catch (RuntimeException exc) {
                log.error("Failed to send transaction. requestId = {}", message.requestId, exc);
            }

            if (isCompleted) {
                this.messageHandler.complete(message);
            }
        }

        log.warn("Terminated handling transaction sending.");
    }

    boolean checkSendingCount() {
        long count = this.dynamoDbAdaptor.fetchSendingCounter();
        if (count <= this.property.getSendingThreshold().longValue()) {
            return true;
        }

        long currentTime = System.currentTimeMillis();
        if (currentTime - this.lastLoggedAt >= TimeUnit.SECONDS.toMillis(15L)) {
            this.lastLoggedAt = currentTime;
            log.warn("Exceeded the threshold of sending transaction. For a while waiting to send transaction."
                             + " current sending count : {}", Long.valueOf(count));
        }

        return false;
    }

    boolean handleTransaction(String requestId) {
        TransactionValue transaction = this.dynamoDbAdaptor.findTransaction(requestId);

        // transaction_queue テーブルからの取得が失敗した場合は処理を中断し、SQS は完了状態にはしない
        if (transaction == null) {
            log.warn("Not found transaction. requestId : {}", requestId);
            return false;
        }

        if (StringUtils.hasText(transaction.traceId)) {
            MDC.put("traceId", transaction.traceId);
        }

        try {
            return this.transactionSender.handleTransaction(requestId, transaction);
        } finally {
            MDC.remove("traceId");
        }
    }
}

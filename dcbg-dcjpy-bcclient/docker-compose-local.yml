version: '3.8'
services:
  bcclient:
    build:
      context: .
      dockerfile: bcclient/Dockerfile
    container_name: ${ZONE_NAME}-bcclient
    ports:
      - "${APP_PORT}:8081"
    environment:
      - CONTRACT_BUCKET_NAME=abijson
      - EXTERNAL_CONTRACT_BUCKET_NAME=external-abijson
      - SERVER_PORT=8081
      - SPRING_PROFILES_ACTIVE=local
      - LOCAL_STACK_ENDPOINT=http://**********:${LOCAL_STACK_ENDPOINT_PORT}
      - LOCALSTACK_HOST=**********
      - WEBSOCKET_URI_HOST=**********
      - WEBSOCKET_URI_PORT=${WEBSOCKET_URI_PORT}
      - SUB_WEBSOCKET_URI_HOST=**********
      - SUB_WEBSOCKET_URI_PORT=${SUB_WEBSOCKET_URI_PORT}
      - S3_LOCAL_ENDPOINT=http://**********
      - S3_LOCAL_ENDPOINT_PORT=${S3_LOCAL_ENDPOINT_PORT}

  bcclient-stream:
    build:
      context: .
      dockerfile: bcclient-stream/Dockerfile
    container_name: ${ZONE_NAME}-bcclient-stream
    environment:
      - CONTRACT_BUCKET_NAME=abijson
      - EXTERNAL_CONTRACT_BUCKET_NAME=external-abijson
      - SPRING_PROFILES_ACTIVE=local
      - LOCAL_STACK_ENDPOINT=http://**********:${LOCAL_STACK_ENDPOINT_PORT}
      - WEBSOCKET_URI_HOST=**********
      - WEBSOCKET_URI_PORT=${WEBSOCKET_URI_PORT}
      - SUB_WEBSOCKET_URI_HOST=**********
      - SUB_WEBSOCKET_URI_PORT=${SUB_WEBSOCKET_URI_PORT}
      - S3_LOCAL_ENDPOINT=http://**********
      - S3_LOCAL_ENDPOINT_PORT=${S3_LOCAL_ENDPOINT_PORT}

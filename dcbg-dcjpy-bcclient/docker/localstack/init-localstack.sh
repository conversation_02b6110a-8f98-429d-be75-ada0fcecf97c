#!/usr/bin/env bash

# set -euo pipefail

LOCALSTACK_HOST=localhost
AWS_REGION=ap-northeast-1

create_queue() {
    local QUEUE_NAME=$1
    awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
      sqs create-queue --queue-name "${QUEUE_NAME}" \
      --region ${AWS_REGION} \
      --attributes FifoQueue=true,VisibilityTimeout=30
}

create_dynamodb_table_transaction_queue() {
  awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
    dynamodb create-table \
    --region ${AWS_REGION} \
    --table-name transaction_queue \
    --attribute-definitions \
      AttributeName=request_id,AttributeType=S \
    --key-schema AttributeName=request_id,KeyType=HASH \
    --provisioned-throughput ReadCapacityUnits=100,WriteCapacityUnits=100
}

create_dynamodb_table_transaction_result() {
  awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
    dynamodb create-table \
    --region ${AWS_REGION} \
    --table-name transaction_result \
    --attribute-definitions \
      AttributeName=transaction_hash,AttributeType=S \
    --key-schema AttributeName=transaction_hash,KeyType=HASH \
    --provisioned-throughput ReadCapacityUnits=100,WriteCapacityUnits=100
}

create_dynamodb_table_sending_counter() {
  awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
    dynamodb create-table \
    --region ${AWS_REGION} \
    --table-name sending_counter \
    --attribute-definitions \
      AttributeName=counter_name,AttributeType=S \
    --key-schema AttributeName=counter_name,KeyType=HASH \
    --provisioned-throughput ReadCapacityUnits=100,WriteCapacityUnits=100
}

set_ttl_transaction_queue() {
  aws dynamodb update-time-to-live --table-name transaction_queue \
  --time-to-live-specification "Enabled=true, AttributeName=expires_at" \
  --endpoint-url http://${LOCALSTACK_HOST}:4566 \
  --region ${AWS_REGION}
}

set_secondary_index_transaction_queue() {
    awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
      dynamodb update-table \
      --region ${AWS_REGION} \
      --table-name transaction_queue \
      --attribute-definitions AttributeName=transaction_hash,AttributeType=S \
      --global-secondary-index-updates \
      '[{"Create":{"IndexName": "transaction_hash_index","KeySchema":[{"AttributeName":"transaction_hash","KeyType":"HASH"}],"ProvisionedThroughput": {"ReadCapacityUnits": 10,"WriteCapacityUnits": 10},"Projection":{"ProjectionType":"ALL"}}}]'
}

echo "Start to configure LocalStack."
DYNAMODB_REMOVE_EXPIRED_ITEMS=1

create_queue "dcjpy_bcclient_queue_send-transaction.fifo"

create_dynamodb_table_transaction_queue
create_dynamodb_table_transaction_result
create_dynamodb_table_sending_counter
set_ttl_transaction_queue
set_secondary_index_transaction_queue
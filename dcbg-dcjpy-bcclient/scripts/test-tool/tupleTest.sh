#!/bin/bash

# テスト実施方法
# 1.https://github.com/decurret-lab/dcbg-dcf-contract-pracをローカル上にクローンする
# 2.ローカルでBESUのnetworkを立ち上げておく
# 3.cd ./dcbg-dcf-contract-prac/EncodeDecodeTest
# 4.npm i
# 5.sh ./scripts/migrate.shを実行する
# 6.buildフォルダにabiができるのでそれをBCClientのminioに配置する
# 7.BCClientをローカルで起動して、このシェルを実行する

#=============================================================================
# 共通の関数定義

function call_bcclient () {
  local CONTRACT_NAME=$1
  local METHOD=$2
  local ARGS=$3

  local TARGET_RESPONSE=$(\
    curl -s -X POST -H "Content-Type: application/json" http://localhost:8081/call \
         -d "{\"contractName\":\"${CONTRACT_NAME}\", \"method\":\"${METHOD}\", \
                 \"args\":${ARGS}}"
  )
  echo "${TARGET_RESPONSE}"
}

#=============================================================================

function test_primitive_encode () {
  CASE_NO=$1
  METHOD=$2
  PARAM=$3

  ARGS="{\"param\":${PARAM}}"
  EXPECT='OK'

  CONTRACT_NAME=PrimitiveTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -r ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != ${EXPECT} ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_primitive_decode () {
  CASE_NO=$1
  METHOD=$2
  EXPECT=$3

  ARGS="{}"

  CONTRACT_NAME=PrimitiveTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -r ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != ${EXPECT} ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_staticArray_encode () {
  CASE_NO=$1
  METHOD=$2
  PARAM=$3

  ARGS="{\"param\":${PARAM}}"
  EXPECT='OK'

  CONTRACT_NAME=StaticArrayTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -r ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_staticArray_decode () {
  CASE_NO=$1
  METHOD=$2
  EXPECT=$3

  ARGS="{}"

  CONTRACT_NAME=StaticArrayTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -c ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_staticTupleTest_encode () {
  CASE_NO=$1
  METHOD=$2
  PARAM=$3

  ARGS="{\"param\":${PARAM}}"
  EXPECT='OK'

  CONTRACT_NAME=StaticTupleTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -r ".result1")

  # 戻り値が正常であること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_staticTupleTest_decode () {
  CASE_NO=$1
  METHOD=$2
  EXPECT=$3

  ARGS="{}"

  CONTRACT_NAME=StaticTupleTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -c ".result1")

  # 戻り値が正常であること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_dynamicArrayTest_encode () {
  CASE_NO=$1
  METHOD=$2
  PARAM=$3

  ARGS="{\"param\":${PARAM}}"
  EXPECT='OK'

  CONTRACT_NAME=DynamicArrayTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -r ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_dynamicArrayTest_decode () {
  CASE_NO=$1
  METHOD=$2
  EXPECT=$3

  ARGS="{}"

  CONTRACT_NAME=DynamicArrayTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -c ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_dynamicTupleTest_encode () {
  CASE_NO=$1
  METHOD=$2
  PARAM=$3

  ARGS="{\"param\":${PARAM}}"
  EXPECT='OK'

  CONTRACT_NAME=DynamicTupleTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -r ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_dynamicTupleTest_decode () {
  CASE_NO=$1
  METHOD=$2
  EXPECT=$3

  ARGS="{}"

  CONTRACT_NAME=DynamicTupleTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -c ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_staticNestTest_encode () {
  CASE_NO=$1
  METHOD=$2
  PARAM=$3

  ARGS="{\"param\":${PARAM}}"
  EXPECT='OK'

  CONTRACT_NAME=StaticNestTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -r ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_staticNestTest_decode () {
  CASE_NO=$1
  METHOD=$2
  EXPECT=$3

  ARGS="{}"

  CONTRACT_NAME=StaticNestTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -c ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_dynamicNestTest_encode () {
  CASE_NO=$1
  METHOD=$2
  PARAM=$3

  ARGS="{\"param\":${PARAM}}"
  EXPECT='OK'

  CONTRACT_NAME=DynamicNestTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -r ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_dynamicNestTest_decode () {
  CASE_NO=$1
  METHOD=$2
  EXPECT=$3

  ARGS="{}"

  CONTRACT_NAME=DynamicNestTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -c ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_nestOfNestTest_encode () {
  CASE_NO=$1
  METHOD=$2
  PARAM=$3

  ARGS="{\"param\":${PARAM}}"
  EXPECT='OK'

  CONTRACT_NAME=NestOfNestTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -r ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

function test_nestOfNestTest_decode () {
  CASE_NO=$1
  METHOD=$2
  EXPECT=$3

  ARGS="{}"

  CONTRACT_NAME=NestOfNestTest
  RESPONSE=$(call_bcclient "${CONTRACT_NAME}" "${METHOD}" "${ARGS}")
  RESPONSE_DETAIL=$(echo "${RESPONSE}" | jq -r ".data" | jq -c ".result1")

  # リクエストのパラメータと実行結果が同じであること
  if [ "${RESPONSE_DETAIL}" != "${EXPECT}" ]; then
        echo "${CASE_NO}, RESPONSE NG : ${RESPONSE}"
        return
  fi
  echo "${CASE_NO},RESPONSE OK"
}

#=============================================================================
echo "----- start $(TZ=JST-9 date '+%Y-%m-%d %H:%M:%S')"

test_primitive_encode "CASE_NO:test#001-1" encodeUint8 1
test_primitive_decode "CASE_NO:test#001-2" decodeUint8 1
test_primitive_encode "CASE_NO:test#002-1" encodeInt8 2
test_primitive_decode "CASE_NO:test#002-2" decodeInt8 2
test_primitive_encode "CASE_NO:test#003-1" encodeAddress 3
test_primitive_decode "CASE_NO:test#003-2" decodeAddress ******************************************
test_primitive_encode "CASE_NO:test#004-1" encodeUint 4
test_primitive_decode "CASE_NO:test#004-2" decodeUint 4
test_primitive_encode "CASE_NO:test#005-1" encodeInt 5
test_primitive_decode "CASE_NO:test#005-2" decodeInt 5
test_primitive_encode "CASE_NO:test#006-1" encodeBool true
test_primitive_decode "CASE_NO:test#006-2" decodeBool true
test_primitive_encode "CASE_NO:test#007-1" encodeByte2 \"0x0123\"
test_primitive_decode "CASE_NO:test#007-2" decodeByte2 0x0123
test_primitive_encode "CASE_NO:test#008-1-1" encodeBytes \"0x0000000000000000000000000000000000000000000000000000000000000001\"
test_primitive_encode "CASE_NO:test#008-1-2" encodeBytes2 \"0x0000000000030046030f26f462d7ac21a27eb9d53fff233c7acd12d87e96aff2\"
test_primitive_decode "CASE_NO:test#008-2-1" decodeBytes 0x0000000000000000000000000000000000000000000000000000000000000001
test_primitive_decode "CASE_NO:test#008-2-2" decodeBytes2 0x0000000000030046030f26f462d7ac21a27eb9d53fff233c7acd12d87e96aff2
test_primitive_encode "CASE_NO:test#009-1-1" encodeString \"abcdeabcdeabcdeabcdeabcdeabcdeab\"
test_primitive_encode "CASE_NO:test#009-1-2" encodeString2 \"aaaaaaaあああああああiiiiiiiイイイイイイイuuuuuuuううううううう\"
test_primitive_decode "CASE_NO:test#009-2-1" decodeString abcdeabcdeabcdeabcdeabcdeabcdeab
test_primitive_decode "CASE_NO:test#009-2-2" decodeString2 aaaaaaaあああああああiiiiiiiイイイイイイイuuuuuuuううううううう

test_staticArray_encode "CASE_NO:test#010-1" encodeStaticArrayUint16 '[1,2]'
test_staticArray_decode "CASE_NO:test#010-2" decodeStaticArrayUint16 '["1","2"]'
test_staticArray_encode "CASE_NO:test#011-1" encodeStaticArrayIntM '[3,4]'
test_staticArray_decode "CASE_NO:test#011-2" decodeStaticArrayIntM '["1","2"]'
test_staticArray_encode "CASE_NO:test#012-1" encodeStaticArrayAddress '[5,6]'
test_staticArray_decode "CASE_NO:test#012-2" decodeStaticArrayAddress '["******************************************","******************************************"]'
test_staticArray_encode "CASE_NO:test#013-1" encodeStaticArrayUint '[7,8]'
test_staticArray_decode "CASE_NO:test#013-2" decodeStaticArrayUint '["1","2"]'
test_staticArray_encode "CASE_NO:test#014-1" encodeStaticArrayInt '[9,10]'
test_staticArray_decode "CASE_NO:test#014-2" decodeStaticArrayInt '["1","2"]'
test_staticArray_encode "CASE_NO:test#015-1" encodeStaticArrayBool '[true,false]'
test_staticArray_decode "CASE_NO:test#015-2" decodeStaticArrayBool '[true,false]'
test_staticArray_encode "CASE_NO:test#016-1" encodeStaticArrayByteM '["0x0123","0x0567"]'
test_staticArray_decode "CASE_NO:test#016-2" decodeStaticArrayByteM '["0x0123","0x4567"]'

test_staticTupleTest_encode "CASE_NO:test#017-1" encodeOneTuple '{"item1":1}'
test_staticTupleTest_decode "CASE_NO:test#017-2" decodeOneTuple '{"item1":"1"}'
test_staticTupleTest_encode "CASE_NO:test#018-1" encodeTwoTuple '{"item1":1,"item2":2}'
test_staticTupleTest_decode "CASE_NO:test#018-2" decodeTwoTuple '{"item1":"1","item2":"******************************************"}'
test_staticTupleTest_encode "CASE_NO:test#019-1" encodeThreeTuple '{"item1":1,"item2":2,"item3":true}'
test_staticTupleTest_decode "CASE_NO:test#019-2" decodeThreeTuple '{"item1":"1","item2":"2","item3":true}'

test_dynamicArrayTest_encode "CASE_NO:test#020-1" encodeDynamicArrayBytes0 '[]'
test_dynamicArrayTest_decode "CASE_NO:test#020-2" decodeDynamicArrayBytes0 '[]'
test_dynamicArrayTest_encode "CASE_NO:test#021-1" encodeDynamicArrayBytes1 '["0x0000000000000000000000000000000000000000000000000000000000000001"]'
test_dynamicArrayTest_decode "CASE_NO:test#021-2" decodeDynamicArrayBytes1 '["0x0000000000000000000000000000000000000000000000000000000000000001"]'
test_dynamicArrayTest_encode "CASE_NO:test#022-1" encodeDynamicArrayString1 '["abcd"]'
test_dynamicArrayTest_decode "CASE_NO:test#022-2" decodeDynamicArrayString1 '["abcd"]'
test_dynamicArrayTest_encode "CASE_NO:test#023-1" encodeDynamicArrayBytes10 '["0x0000000000000000000000000000000000000000000000000000000000000000","0x0000000000000000000000000000000000000000000000000000000000000001","0x0000000000000000000000000000000000000000000000000000000000000002","0x0000000000000000000000000000000000000000000000000000000000000003","0x0000000000000000000000000000000000000000000000000000000000000004","0x0000000000000000000000000000000000000000000000000000000000000005","0x0000000000000000000000000000000000000000000000000000000000000006","0x0000000000000000000000000000000000000000000000000000000000000007","0x0000000000000000000000000000000000000000000000000000000000000008","0x0000000000000000000000000000000000000000000000000000000000000009"]'
test_dynamicArrayTest_decode "CASE_NO:test#023-2" decodeDynamicArrayBytes10 '["0x0000000000000000000000000000000000000000000000000000000000000000","0x0000000000000000000000000000000000000000000000000000000000000001","0x0000000000000000000000000000000000000000000000000000000000000002","0x0000000000000000000000000000000000000000000000000000000000000003","0x0000000000000000000000000000000000000000000000000000000000000004","0x0000000000000000000000000000000000000000000000000000000000000005","0x0000000000000000000000000000000000000000000000000000000000000006","0x0000000000000000000000000000000000000000000000000000000000000007","0x0000000000000000000000000000000000000000000000000000000000000008","0x0000000000000000000000000000000000000000000000000000000000000009"]'
test_dynamicArrayTest_encode "CASE_NO:test#024-1" encodeDynamicArrayString10 '["aaa","bbb","ccc","ddd","eee","fff","ggg","hhh","iii","jjj"]'
test_dynamicArrayTest_decode "CASE_NO:test#024-2" decodeDynamicArrayString10 '["ab","cd","ef","gh","ij","kl","mn","op","qr","st"]'
test_dynamicArrayTest_decode "CASE_NO:test#025-2" decodeDynamicArrayOneBytes '["0x0000000000000000000000000000000000000000000000000000000000000001"]'
test_dynamicArrayTest_decode "CASE_NO:test#026-2" decodeDynamicArrayOneString '["abcd"]'
test_dynamicArrayTest_decode "CASE_NO:test#027-2" decodeDynamicArrayTenBytes '["0x0000000000000000000000000000000000000000000000000000000000000000","0x0000000000000000000000000000000000000000000000000000000000000001","0x0000000000000000000000000000000000000000000000000000000000000002","0x0000000000000000000000000000000000000000000000000000000000000003","0x0000000000000000000000000000000000000000000000000000000000000004","0x0000000000000000000000000000000000000000000000000000000000000005","0x0000000000000000000000000000000000000000000000000000000000000006","0x0000000000000000000000000000000000000000000000000000000000000007","0x0000000000000000000000000000000000000000000000000000000000000008","0x0000000000000000000000000000000000000000000000000000000000000009"]'
test_dynamicArrayTest_decode "CASE_NO:test#028-2" decodeDynamicArrayTenString '["ab","cd","ef","gh","ij","kl","mn","op","qr","st"]'

test_dynamicTupleTest_encode "CASE_NO:test#029-1" encodeOneTuple '{"item1":"ab"}'
test_dynamicTupleTest_decode "CASE_NO:test#029-2" decodeOneTuple '{"item1":"ab"}'
test_dynamicTupleTest_encode "CASE_NO:test#030-1" encodeTwoTuple1 '{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":1}'
test_dynamicTupleTest_decode "CASE_NO:test#030-2" decodeTwoTuple1 '{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"2"}'
test_dynamicTupleTest_encode "CASE_NO:test#031-1" encodeTwoTuple2 '{"item1":1,"item2":"efgh"}'
test_dynamicTupleTest_decode "CASE_NO:test#031-2" decodeTwoTuple2 '{"item1":"1","item2":"efgh"}'
test_dynamicTupleTest_encode "CASE_NO:test#032-1" encodeTwoTuple3 '{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"efgh"}'
test_dynamicTupleTest_decode "CASE_NO:test#032-2" decodeTwoTuple3 '{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"efgh"}'
test_dynamicTupleTest_encode "CASE_NO:test#033-1" encodeThreeTuple1 '{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"******************************************","item3":3}'
test_dynamicTupleTest_decode "CASE_NO:test#033-2" decodeThreeTuple1 '{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"******************************************","item3":"3"}'
test_dynamicTupleTest_encode "CASE_NO:test#034-1" encodeThreeTuple2 '{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"efgh","item3":3}'
test_dynamicTupleTest_decode "CASE_NO:test#034-2" decodeThreeTuple2 '{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"efgh","item3":"3"}'
test_dynamicTupleTest_encode "CASE_NO:test#035-1" encodeThreeTuple3 '{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"efgh","item3":"ijkl"}'
test_dynamicTupleTest_decode "CASE_NO:test#035-2" decodeThreeTuple3 '{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"efgh","item3":"ijkl"}'
test_dynamicTupleTest_encode "CASE_NO:test#036-1" encodeThreeTuple4 '{"item1":true,"item2":"0x0000000000000000000000000000000000000000000000000000000000000001","item3":"0x1122334455667788"}'
test_dynamicTupleTest_decode "CASE_NO:test#036-2" decodeThreeTuple4 '{"item1":true,"item2":"0x0000000000000000000000000000000000000000000000000000000000000001","item3":"0x1122334455667788"}'
test_dynamicTupleTest_encode "CASE_NO:test#037-1" encodeThreeTuple5 '{"item1":"0x00112233445566778899aabbccddeeff","item2":"0x0000000000000000000000000000000000000000000000000000000000000001","item3":"ijkl"}'
test_dynamicTupleTest_decode "CASE_NO:test#037-2" decodeThreeTuple5 '{"item1":"0x00112233445566778899aabbccddeeff","item2":"0x0000000000000000000000000000000000000000000000000000000000000001","item3":"ijkl"}'

test_staticNestTest_encode "CASE_NO:test#038-1" encodeStaticArray '[{"item1":1,"item2":2},{"item1":3,"item2":4}]'
test_staticNestTest_decode "CASE_NO:test#038-2" decodeStaticArray '[{"item1":"1","item2":"2"},{"item1":"3","item2":"4"}]'
test_staticNestTest_encode "CASE_NO:test#039-1" encodeStaticTuple '{"item1":[1,2],"item2":[3,4]}'
test_staticNestTest_decode "CASE_NO:test#039-2" decodeStaticTuple '{"item1":["******************************************","******************************************"],"item2":["3","4"]}'

test_dynamicNestTest_decode "CASE_NO:test#040-2" decodeNestDynamicArray1 '[["aa","bb"],["cc","dd"],["ee","ff"],["gg","hh"],["ii","jj"],["kk","ll"],["mm","nn"],["oo","pp"],["qq","rr"],["ss","tt"]]'
test_dynamicNestTest_encode "CASE_NO:test#041-1" encodeNestDynamicTuple '[{"item1":"0x0000000000000000000000000000000000000000000000000000000000000000","item2":"aaa"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"bbb"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000002","item2":"ccc"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000003","item2":"ddd"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000004","item2":"eee"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000005","item2":"fff"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000006","item2":"ggg"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000007","item2":"hhh"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000008","item2":"iii"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000009","item2":"jjj"}]'
test_dynamicNestTest_decode "CASE_NO:test#041-2" decodeNestDynamicTuple '[{"item1":"0x0000000000000000000000000000000000000000000000000000000000000000","item2":"abcd"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000001","item2":"abcd"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000002","item2":"abcd"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000003","item2":"abcd"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000004","item2":"abcd"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000005","item2":"abcd"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000006","item2":"abcd"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000007","item2":"abcd"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000008","item2":"abcd"},{"item1":"0x0000000000000000000000000000000000000000000000000000000000000009","item2":"abcd"}]'
test_dynamicNestTest_decode "CASE_NO:test#042-2" decodeNestDynamicArray2 '[["1","2"],["3","4"],["5","6"],["7","8"],["9","10"],["11","12"],["13","14"],["15","16"],["17","18"],["19","20"]]'
test_dynamicNestTest_encode "CASE_NO:test#043-1" encodeNestDynamicTuple2 '[{"item1":1,"item2":"******************************************"},{"item1":3,"item2":"0x0000000000000000000000000000000000000004"},{"item1":5,"item2":"0x0000000000000000000000000000000000000006"},{"item1":7,"item2":"0x0000000000000000000000000000000000000008"},{"item1":9,"item2":"0x000000000000000000000000000000000000000a"},{"item1":11,"item2":"0x000000000000000000000000000000000000000c"},{"item1":13,"item2":"0x000000000000000000000000000000000000000e"},{"item1":15,"item2":"0x0000000000000000000000000000000000000010"},{"item1":17,"item2":"0x0000000000000000000000000000000000000012"},{"item1":19,"item2":"0x0000000000000000000000000000000000000014"}]'
test_dynamicNestTest_decode "CASE_NO:test#043-2" decodeNestDynamicTuple2 '[{"item1":"0","item2":"0x0000000000000000000000000000000000000000"},{"item1":"1","item2":"******************************************"},{"item1":"2","item2":"******************************************"},{"item1":"3","item2":"******************************************"},{"item1":"4","item2":"0x0000000000000000000000000000000000000004"},{"item1":"5","item2":"0x0000000000000000000000000000000000000005"},{"item1":"6","item2":"0x0000000000000000000000000000000000000006"},{"item1":"7","item2":"0x0000000000000000000000000000000000000007"},{"item1":"8","item2":"0x0000000000000000000000000000000000000008"},{"item1":"9","item2":"0x0000000000000000000000000000000000000009"}]'
test_dynamicNestTest_decode "CASE_NO:test#044-2" decodeNestDynamicArray3 '[["aa"],["cc"],["ee"],["gg"],["ii"],["kk"],["mm"],["oo"],["qq"],["ss"]]'
test_dynamicNestTest_encode "CASE_NO:test#045-1" encodeNestDynamicTuple3 '[{"item1":1,"item2":11,"item3":"0x0000000000000000000000000000000000000000000000000000000000000000"},{"item1":2,"item2":12,"item3":"0x0000000000000000000000000000000000000000000000000000000000000001"},{"item1":3,"item2":13,"item3":"0x0000000000000000000000000000000000000000000000000000000000000002"},{"item1":4,"item2":14,"item3":"0x0000000000000000000000000000000000000000000000000000000000000003"},{"item1":5,"item2":15,"item3":"0x0000000000000000000000000000000000000000000000000000000000000004"},{"item1":6,"item2":16,"item3":"0x0000000000000000000000000000000000000000000000000000000000000005"},{"item1":7,"item2":17,"item3":"0x0000000000000000000000000000000000000000000000000000000000000006"},{"item1":8,"item2":18,"item3":"0x0000000000000000000000000000000000000000000000000000000000000007"},{"item1":9,"item2":19,"item3":"0x0000000000000000000000000000000000000000000000000000000000000008"},{"item1":10,"item2":20,"item3":"0x0000000000000000000000000000000000000000000000000000000000000009"}]'
test_dynamicNestTest_decode "CASE_NO:test#045-2" decodeNestDynamicTuple3 '[{"item1":"0","item2":"0","item3":"0x0000000000000000000000000000000000000000000000000000000000000000"},{"item1":"1","item2":"1","item3":"0x0000000000000000000000000000000000000000000000000000000000000001"},{"item1":"2","item2":"2","item3":"0x0000000000000000000000000000000000000000000000000000000000000002"},{"item1":"3","item2":"3","item3":"0x0000000000000000000000000000000000000000000000000000000000000003"},{"item1":"4","item2":"4","item3":"0x0000000000000000000000000000000000000000000000000000000000000004"},{"item1":"5","item2":"5","item3":"0x0000000000000000000000000000000000000000000000000000000000000005"},{"item1":"6","item2":"6","item3":"0x0000000000000000000000000000000000000000000000000000000000000006"},{"item1":"7","item2":"7","item3":"0x0000000000000000000000000000000000000000000000000000000000000007"},{"item1":"8","item2":"8","item3":"0x0000000000000000000000000000000000000000000000000000000000000008"},{"item1":"9","item2":"9","item3":"0x0000000000000000000000000000000000000000000000000000000000000009"}]'
test_dynamicNestTest_decode "CASE_NO:test#046-2" decodeNestDynamicArray4 '[["1","2"],["3","4"],["5","6"],["7","8"],["9","10"],["11","12"],["13","14"],["15","16"],["17","18"],["19","20"]]'
test_dynamicNestTest_encode "CASE_NO:test#047-1" encodeNestDynamicTuple4 '[{"item1":1,"item2":2,"item3":"******************************************"},{"item1":4,"item2":5,"item3":"0x0000000000000000000000000000000000000006"},{"item1":7,"item2":8,"item3":"0x0000000000000000000000000000000000000009"},{"item1":10,"item2":11,"item3":"0x000000000000000000000000000000000000000c"},{"item1":13,"item2":14,"item3":"0x000000000000000000000000000000000000000f"},{"item1":16,"item2":17,"item3":"0x0000000000000000000000000000000000000012"},{"item1":19,"item2":20,"item3":"0x0000000000000000000000000000000000000015"},{"item1":22,"item2":23,"item3":"0x0000000000000000000000000000000000000018"},{"item1":25,"item2":26,"item3":"0x000000000000000000000000000000000000001b"},{"item1":28,"item2":29,"item3":"0x000000000000000000000000000000000000001e"}]'
test_dynamicNestTest_decode "CASE_NO:test#047-2" decodeNestDynamicTuple4 '[{"item1":"0","item2":"0","item3":"0x0000000000000000000000000000000000000000"},{"item1":"1","item2":"1","item3":"******************************************"},{"item1":"2","item2":"2","item3":"******************************************"},{"item1":"3","item2":"3","item3":"******************************************"},{"item1":"4","item2":"4","item3":"0x0000000000000000000000000000000000000004"},{"item1":"5","item2":"5","item3":"0x0000000000000000000000000000000000000005"},{"item1":"6","item2":"6","item3":"0x0000000000000000000000000000000000000006"},{"item1":"7","item2":"7","item3":"0x0000000000000000000000000000000000000007"},{"item1":"8","item2":"8","item3":"0x0000000000000000000000000000000000000008"},{"item1":"9","item2":"9","item3":"0x0000000000000000000000000000000000000009"}]'
test_dynamicNestTest_decode "CASE_NO:test#048-2" decodeNestDynamicTuple5 '{"item1":["0x0000000000000000000000000000000000000000000000000000000000000000","0x0000000000000000000000000000000000000000000000000000000000000001","0x0000000000000000000000000000000000000000000000000000000000000002","0x0000000000000000000000000000000000000000000000000000000000000003","0x0000000000000000000000000000000000000000000000000000000000000004","0x0000000000000000000000000000000000000000000000000000000000000005","0x0000000000000000000000000000000000000000000000000000000000000006","0x0000000000000000000000000000000000000000000000000000000000000007","0x0000000000000000000000000000000000000000000000000000000000000008","0x0000000000000000000000000000000000000000000000000000000000000009"],"item2":["abcd","abcd","abcd","abcd","abcd","abcd","abcd","abcd","abcd","abcd"]}'
test_dynamicNestTest_decode "CASE_NO:test#049-2" decodeNestDynamicTuple6 '{"item1":["0","1","2","3","4","5","6","7","8","9"],"item2":["abcd","abcd","abcd","abcd","abcd","abcd","abcd","abcd","abcd","abcd"]}'

test_nestOfNestTest_encode "CASE_NO:test#050-1" encodeNestOfNest1 '{"item1":{"item1":1,"item2":2,"item3":3},"item2":{"item1":4,"item2":5,"item3":6}}'
test_nestOfNestTest_decode "CASE_NO:test#050-2" decodeNestOfNest1 '{"item1":{"item1":"1","item2":"******************************************","item3":"3"},"item2":{"item1":"4","item2":"0x0000000000000000000000000000000000000005","item3":"6"}}'
test_nestOfNestTest_encode "CASE_NO:test#051-1" encodeNestOfNest2 '{"item1": [{"item1":1,"item2":true},{"item1":2,"item2":true}],"item2": [{"item1":3,"item2":true},{"item1":4,"item2":true}],"item3": [{"item1":5,"item2":true},{"item1":6,"item2":true}]}'
test_nestOfNestTest_decode "CASE_NO:test#051-2" decodeNestOfNest2 '{"item1":[{"item1":"1","item2":true},{"item1":"2","item2":false}],"item2":[{"item1":"1","item2":true},{"item1":"2","item2":false}],"item3":[{"item1":"1","item2":true},{"item1":"2","item2":false}]}'
test_nestOfNestTest_decode "CASE_NO:test#052-2" decodeNestOfNest3 '[{"item1":["1"],"item2":["0x616161"]}]'
test_nestOfNestTest_decode "CASE_NO:test#053-2" decodeNestOfNest4 '[{"item1":["aaa"],"item2":["aaa"]}]'
test_nestOfNestTest_decode "CASE_NO:test#054-2" decodeNestOfNest5 '{"item1":["1"],"item2":[{"item1":"8","item2":false}],"item3":["ccc"]}'

echo "----- end $(TZ=JST-9 date '+%Y-%m-%d %H:%M:%S')"
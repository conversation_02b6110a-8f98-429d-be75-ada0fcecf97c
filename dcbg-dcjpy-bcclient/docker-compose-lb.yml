version: '3.8'
services:
  app1:
    build:
      context: .
      dockerfile: Dockerfile.local
    environment:
      - SPRING_PROFILES_ACTIVE=local
      - WEBSOCKET_URI_PORT=1000
      - SUB_WEBSOCKET_URI_PORT=1000
    working_dir: /app
    entrypoint: bash
    command: ./gradlew bootRun
  app2:
    build:
      context: .
      dockerfile: Dockerfile.local
    environment:
      - SPRING_PROFILES_ACTIVE=local
      - WEBSOCKET_URI_PORT=2000
      - SUB_WEBSOCKET_URI_PORT=2000
    working_dir: /app
    entrypoint: bash
    command: ./gradlew bootRun
  app3:
    build:
      context: .
      dockerfile: Dockerfile.local
    environment:
      - SPRING_PROFILES_ACTIVE=local
      - WEBSOCKET_URI_PORT=3000
      - SUB_WEBSOCKET_URI_PORT=3000
    working_dir: /app
    entrypoint: bash
    command: ./gradlew bootRun
  minio:
    image: minio/minio:RELEASE.2021-04-06T23-11-00Z.hotfix.f3cd60697
    ports:
      - 9000:9000
    environment:
      - MINIO_ACCESS_KEY=access123
      - MINIO_SECRET_KEY=secret123
    entrypoint: sh
    command: -c "
      rm -rf /data/*;
      mkdir -p /data/.minio.sys/buckets;
      cp -r /policies/* /data/.minio.sys/;
      cp -r /export/* /data/;
      /usr/bin/minio server /data;
      "
    volumes:
      - ./docker/minio/data:/data
      - ./docker/minio/export:/export
      - ./docker/minio/config:/root/.minio
      - ./docker/minio/policies:/policies
  sqs:
    image: softwaremill/elasticmq-native:1.2.2
    ports:
      - 9324:9324
      - 9325:9325
    volumes:
      - ./docker/elasticmq/elasticmq.conf:/opt/elasticmq.conf
  nginx:
    image: nginx:1.21.3
    ports:
      - 8081:80
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf

# dcbg-dcjpy-bcclient

## 設計資料

API 概要設計: https://decurret.atlassian.net/wiki/spaces/DIG/pages/1146748941/BC+API

内部処理設計: https://decurret.atlassian.net/wiki/spaces/DIG/pages/2613706768/BC+API

## ディレクトリ構成

```
dcbg-dcjpy-bcclient
├── bcclient                    # BCClient に関するモジュールを扱う
│     ├── build.gradle
│     └── src
│
├── bcclient-base               # BCClient および BCClient Stream に共通するモジュールを扱う
│     ├── build.gradle
│     └── src
│
├── bcclient-stream             # BCClient Stream に関するモジュールを扱う
│     ├── build.gradle
│     └── src
│
├── build.common.gradle         # 全モジュール共通の依存関係を定義する
├── build.gradle
├── docker                      # docker compose に利用する、ローカル環境用の構成ファイル
│     ├── entrypoint.sh
│     ├── localstack
│     ├── minio
│     └── minio-external
├── Dockerfile
├── Dockerfile.local
├── README.md
├── docker-compose-lb.yml
├── docker-compose.yml
├── gradle
├── gradlew
├── gradlew.bat
├── lombok.config
├── scripts
└── settings.gradle
```

## ローカル環境でのアプリケーション起動

1. BESU を起動する。
2. docker compose 環境を起動する
    ```shell
    docker compose up
    ```
3. BCClient Stream を起動する
    ```shell
    ./gradlew bcclient-stream:bootRun
    ```
4. BCClient を起動する
    ```shell
    ./gradlew bcclient:bootRun
    ```

---
# 以下は旧バージョンの記載であることに注意

## 環境構築

Confluence ページを参照
https://decurret.atlassian.net/wiki/spaces/DIG/pages/1439400032/BC

## 起動（Docker）

```
./scripts/run-local.sh
```

## ビルド

```
./gradlew build
```

## ユニットテスト

```
./gradlew test
```

## 起動（Docker マルチコンテナ）

複数台で負荷分散した際のスループット計測（Confluence）
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BC+Besu

```
./scripts/run-multi-app-local.sh
```

## API 実行例

### send API

```
curl -X POST -H "Content-Type: application/json" \
   http://localhost:8081/send -d @- <<EOF | jq .
{
   "requestId": "12345",
   "zoneId": "3000",
   "contractName": "Provider",
   "method": "addProvider",
   "args": {
      "provID" : "101",
      "deadline": "100000000000000",
      "signature": "0x00000000000000000000000000000000",
      "traceId": "xxxxxxxxx"
   }
}
EOF
```

### call API

```
curl -X POST -H "Content-Type: application/json" \
   http://localhost:8081/call -d @- <<EOF | jq .
{
   "zoneId": "3000",
   "contractName": "Provider",
   "method": "hasProvID",
   "args": {
      "provID" : "101",
      "chkEnabled" : true
   }
}
EOF
```

### transfer

```
curl -X POST -H "Content-Type: application/json" \
   http://localhost:8081/send  -d @- <<EOF | jq .
{
   "requestId": "12345",
   "zoneId": "3000",
   "contractName": "Token",
   "method": "transferSingle",
   "args": {
      "validatorId": 13107,
      "fromAccountId": 20481,
      "sendAccountId": 20481,
      "toAccountId": 20480,
      "amount": 1,
      "miscValue1": "0x0000000000000000000000000000000000000000000000000000000000000000",
      "miscValue2": "0x0000000000000000000000000000000000000000000000000000000000000000",
      "tokenId": 36864,
      "deadline": **********,
      "signature": "0x0cc895a10c0047cc7c61500137b9fe640fea3ca0a0fd5e970c0cbb5357244f99433ba024ec871564aa362cee5a62617c1790545c8b1e77ee86c85feb8a0778c41b",
      "traceId": "xxxxxxxxx"
   }
}
EOF
```

<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Gradle Imported" enabled="true">
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.24/13a394eed5c4f9efb2a6d956e2086f1d81e857d9/lombok-1.18.24.jar" />
        </processorPath>
        <module name="dcbg-dcjpy-bcclient.bcclient.main" />
        <module name="dcbg-dcjpy-bcclient.bcclient-base.main" />
        <module name="dcbg-dcjpy-bcclient.bcclient-stream.main" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel target="17" />
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="dcbg-dcjpy-bcclient.bcclient" options="-parameters" />
      <module name="dcbg-dcjpy-bcclient.bcclient-base" options="-parameters" />
      <module name="dcbg-dcjpy-bcclient.bcclient-base.main" options="-parameters" />
      <module name="dcbg-dcjpy-bcclient.bcclient-base.test" options="-parameters" />
      <module name="dcbg-dcjpy-bcclient.bcclient-stream" options="-parameters" />
      <module name="dcbg-dcjpy-bcclient.bcclient-stream.main" options="-parameters" />
      <module name="dcbg-dcjpy-bcclient.bcclient-stream.test" options="-parameters" />
      <module name="dcbg-dcjpy-bcclient.bcclient.main" options="-parameters" />
      <module name="dcbg-dcjpy-bcclient.bcclient.test" options="-parameters" />
    </option>
  </component>
</project>
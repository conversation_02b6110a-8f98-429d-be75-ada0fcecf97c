package com.decurret_dcp.dcjpy.bcclient.base.transaction;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.web3j.abi.DefaultFunctionEncoder;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Type;

import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractAbi;
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractFunction;
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractInputParameter;
import com.decurret_dcp.dcjpy.bcclient.base.exception.BadRequestException;
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class EthTransactionCommand {

    private static final DefaultFunctionEncoder FUNCTION_ENCODER = new DefaultFunctionEncoder();

    public final String requestId;

    public final ZoneId zoneId;

    public final ContractAbi contractAbi;

    public final ContractFunction contractFunction;

    public final Map<String, Object> parameters;

    public final String traceId;

    public String contractAddress() {
        return this.contractAbi.getAddress();
    }

    public String contractName() {
        return this.contractAbi.getName();
    }

    public String functionName() {
        return this.contractFunction.getName();
    }

    /**
     * 指定された引数に、コントラクト関数の引数から不足がないか検証する。
     */
    public void verify() {
        List<ContractInputParameter> inputParameters = this.contractFunction.getInputParameterList();
        for (ContractInputParameter param : inputParameters) {
            Object inputValue = this.parameters.get(param.getName());
            if (inputValue == null) {
                throw new BadRequestException(param.getName() + " does not exists.");
            }

            try {
                param.toAbiValue(inputValue);
            } catch (RuntimeException exc) {
                throw new BadRequestException(
                        "The value of " + param.getName() + " is invalid. value = "
                                + inputValue + ", cause : " + exc.getMessage(), exc
                );
            }
        }
    }

    /**
     * エンコードした関数を生成する。
     *
     * @return エンコード済み関数
     */
    public String encode() {
        // argsをEthDataに変換しFunctionをエンコードする
        List<ContractInputParameter> inputParameters = this.contractFunction.getInputParameterList();

        @SuppressWarnings("rawtypes")
        List<Type> inputAbiValues = inputParameters.stream().map((param) -> {
            Object inputValue = this.parameters.get(param.getName());
            if (inputValue == null) {
                throw new BadRequestException(param.getName() + " does not exists");
            }

            return param.toAbiValue(inputValue);
        }).collect(Collectors.toList());

        // input のエンコードに outputParameters は利用していないので、空配列を指定している
        Function function = new Function(this.contractFunction.getName(), inputAbiValues, List.of());

        return FUNCTION_ENCODER.encodeFunction(function);
    }
}

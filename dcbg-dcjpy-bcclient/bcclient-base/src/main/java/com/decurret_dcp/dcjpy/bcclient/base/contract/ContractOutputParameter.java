package com.decurret_dcp.dcjpy.bcclient.base.contract;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.web3j.abi.datatypes.Type;

import lombok.Getter;

@Getter
public class ContractOutputParameter<DECODE_TYPE> {

    private static final Pattern ARRAY_SUFFIX = Pattern.compile("\\[(\\d*)]");

    private final String name;
    private final String solidityType;
    private final AbiTypeDecoder<?, DECODE_TYPE> decoder;
    private final List<ContractOutputParameter<?>> components;

    private ContractOutputParameter(String name, String solidityType, AbiTypeDecoder<?, DECODE_TYPE> decoder) {
        this(name, solidityType, decoder, List.of());
    }

    private ContractOutputParameter(
            String name, String solidityType, AbiTypeDecoder<?, DECODE_TYPE> decoder,
            List<ContractOutputParameter<?>> components
    ) {

        this.name = name;
        this.solidityType = solidityType;
        this.decoder = decoder;
        this.components = List.copyOf(components);
    }

    public boolean isDynamic() {
        return this.decoder.isDynamicType();
    }

    public DECODE_TYPE decodeToNative(String encodedValue) {
        return this.decoder.decodeToNative(encodedValue);
    }

    public static ContractOutputParameter<?> create(String paramName, String solidityType) {
        int indexOfBracket = solidityType.indexOf("[");
        String baseSolidityType = (indexOfBracket >= 0) ? solidityType.substring(0, indexOfBracket) : solidityType;
        AbiTypeDecoder<?, ?> baseDecoder = AbiTypeDecoder.getDecoder(baseSolidityType);

        AbiTypeDecoder<?, ?> decoder = wrapArrayDecoder(solidityType, baseDecoder);

        return new ContractOutputParameter<>(paramName, baseSolidityType, decoder);
    }

    public static ContractOutputParameter<?> create(
            String paramName, String solidityType, List<ContractOutputParameter<?>> components
    ) {
        final boolean isDynamic = components.stream().anyMatch(component -> component.isDynamic());
        AbiTypeDecoder<Type<Map<String, Object>>, Map<String, Object>> tupleDecoder = new AbiTypeDecoder<>() {

            @Override
            public boolean isDynamicType() {
                return isDynamic;
            }

            @Override
            public int encodedByteLength() {
                int length = 0;
                for (ContractOutputParameter<?> component : components) {
                    length += component.decoder.encodedByteLength();
                }

                return length;
            }

            @Override
            public Map<String, Object> decodeToNative(String input) {
                List<AbiTypeDecoder<?, ?>> decoders = components.stream().map(component -> component.decoder)
                        .collect(Collectors.toList());
                List<?> decodedValues = AbiTypeDecoder.decodeTupleToNative(input, decoders);

                Map<String, Object> tupleValue = new LinkedHashMap<>();
                for (int index = 0; index < components.size(); index++) {
                    ContractOutputParameter<?> component = components.get(index);
                    Object decodedValue = decodedValues.get(index);
                    tupleValue.put(component.getName(), decodedValue);
                }

                return tupleValue;
            }

            @Override
            public Type<Map<String, Object>> decode(String input) {
                throw new UnsupportedOperationException("decode to tuple of abi value is not supported.");
            }
        };

        AbiTypeDecoder<?, ?> decoder = wrapArrayDecoder(solidityType, tupleDecoder);

        return new ContractOutputParameter<>(paramName, solidityType, decoder, components);
    }

    private static AbiTypeDecoder<?, ?> wrapArrayDecoder(String solidityType, AbiTypeDecoder<?, ?> fromDecoder) {
        // reference from org.web3j.abi.TypeReference.makeTypeReference(String, boolean, boolean)

        Matcher nextSquareBrackets = ARRAY_SUFFIX.matcher(solidityType);
        if (nextSquareBrackets.find() == false) {
            return fromDecoder;
        }

        AbiTypeDecoder<?, ?> decoder = fromDecoder;
        int lastReadStringPosition = nextSquareBrackets.start();
        final int len = solidityType.length();

        // for each [\d*], wrap the previous TypeReference in an array
        while (lastReadStringPosition < len) {
            AbiTypeDecoder<?, ?> baseDecoder = decoder;
            String arraySize = nextSquareBrackets.group(1);
            if ((arraySize == null) || arraySize.equals("")) {
                decoder = AbiTypeDecoder.dynamicArrayDecoder(baseDecoder);
            } else {
                int size = Integer.parseInt(arraySize);
                decoder = AbiTypeDecoder.fixedSizeArrayDecoder(size, baseDecoder);
            }

            lastReadStringPosition = nextSquareBrackets.end();
            nextSquareBrackets = ARRAY_SUFFIX.matcher(solidityType);
            // cant find any more [] and string isn't fully parsed
            if ((nextSquareBrackets.find(lastReadStringPosition) == false) && (lastReadStringPosition != len)) {
                throw new IllegalStateException("Unable to create abi decoder from " + solidityType);
            }
        }

        return decoder;
    }
}

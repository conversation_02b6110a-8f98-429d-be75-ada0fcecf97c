package com.decurret_dcp.dcjpy.bcclient.base.transaction;

import com.decurret_dcp.dcjpy.bcclient.base.credential.Web3jCredentialHolder;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool;

import org.web3j.crypto.Credentials;
import org.web3j.crypto.ECKeyPair;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.response.EthGetTransactionCount;

import java.io.IOException;
import java.math.BigInteger;
import java.util.concurrent.atomic.AtomicReference;

public class NonceManager {

    private final Credentials credentials;

    private final AtomicReference<BigInteger> ref;

    NonceManager(Credentials credentials, BigInteger nonce) {
        this.credentials = credentials;
        this.ref = new AtomicReference<>(nonce);
    }

    public static NonceManager create(
            MainWebSocketConnectionPool connectionPool, Web3jCredentialHolder credentialHolder
    ) {
        Credentials credentials = credentialHolder.getCredentials();
        BigInteger nonce = initializeNonce(connectionPool.getWebSocketConnection(), credentials);

        return new NonceManager(credentials, nonce);
    }

    private static BigInteger initializeNonce(Web3j web3j, Credentials credentials) {
        String address = credentials.getAddress();

        try {
            EthGetTransactionCount getTransaction =
                    web3j.ethGetTransactionCount(address, DefaultBlockParameterName.PENDING).send();
            return getTransaction.getTransactionCount();
        } catch (IOException ioExc) {
            throw new RuntimeException("Failed to fetch nonce value.", ioExc);
        }
    }

    public BigInteger getNonce() {
        return this.ref.getAndUpdate(previous -> previous.add(BigInteger.ONE));
    }

    public String getNonceKey() {
        ECKeyPair ecKeyPair = this.credentials.getEcKeyPair();

        // nonce_key は DynamoDB の transaction_queue テーブルに保存する値。
        // 万が一、 DLT への send transaction が失敗して nonce 欠損が起こった場合に再送できるよう、
        // 秘密鍵および公開鍵の組み合わせで保存する
        return ecKeyPair.getPrivateKey().toString() + ":" + ecKeyPair.getPublicKey().toString();
    }

    public Credentials getCredentials() {
        return this.credentials;
    }
}

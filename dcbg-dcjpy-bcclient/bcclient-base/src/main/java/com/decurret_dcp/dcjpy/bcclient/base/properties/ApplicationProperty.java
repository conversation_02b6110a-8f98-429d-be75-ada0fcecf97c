package com.decurret_dcp.dcjpy.bcclient.base.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix="app")
public class ApplicationProperty {

    private AbiFormat abiFormat;

    // メインWebSocket
    private String webSocketUriHost;
    private String webSocketUriPort;

    // サブWebSocket
    private boolean useSubWebSocket;
    private String subWebSocketUriHost;
    private String subWebSocketUriPort;

    private Long requestTimeoutSec;
    private Long gasLimit;

    /** send transaction 同時送信数の閾値。 */
    private Long sendingThreshold;

    public void setAbiFormat(String abiFormat) {
        this.abiFormat = AbiFormat.get(abiFormat);
    }
}
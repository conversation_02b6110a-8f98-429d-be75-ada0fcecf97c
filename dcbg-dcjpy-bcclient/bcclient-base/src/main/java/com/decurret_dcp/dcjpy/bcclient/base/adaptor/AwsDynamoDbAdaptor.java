package com.decurret_dcp.dcjpy.bcclient.base.adaptor;

import java.math.BigInteger;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bcclient.base.exception.RequestIdDuplicatedException;
import com.decurret_dcp.dcjpy.bcclient.base.properties.DynamoDbProperty;
import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthTransactionCommand;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionResultValue;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionStatus;
import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionValue;
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeAction;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.AttributeValueUpdate;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;
import software.amazon.awssdk.services.dynamodb.model.GetItemRequest;
import software.amazon.awssdk.services.dynamodb.model.GetItemResponse;
import software.amazon.awssdk.services.dynamodb.model.PutItemRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryResponse;
import software.amazon.awssdk.services.dynamodb.model.ResourceNotFoundException;
import software.amazon.awssdk.services.dynamodb.model.UpdateItemRequest;

@RequiredArgsConstructor
@Component
@Slf4j
public class AwsDynamoDbAdaptor {

    private static final String TABLE_TRANSACTION_QUEUE = "transaction_queue";

    private static final String TABLE_TRANSACTION_RESULT = "transaction_result";

    private static final String TABLE_SENDING_COUNTER = "sending_counter";

    private static final long EXPIRATION_DAYS = 7L;

    private static final Map<String, AttributeValue> KEY_SENDING_COUNTER =
            Map.ofEntries(Map.entry("counter_name", Encoder.string("sending")));

    private final DynamoDbClient dynamoDbClient;

    private final DynamoDbProperty dynamoDbProperty;

    /**
     * DynamoDB のトランザクション送信前管理テーブルに send transaction を登録する。
     *
     * @param command send transaction
     */
    public void register(EthTransactionCommand command) {
        Map<String, AttributeValue> attributes = Map.ofEntries(
                Map.entry("request_id", Encoder.string(command.requestId)),
                Map.entry("transaction_status", Encoder.string(TransactionStatus.QUEUING.getValue())),
                Map.entry("zone_id", Encoder.number(command.zoneId.getValue())),
                Map.entry("contract_name", Encoder.string(command.contractName())),
                Map.entry("method", Encoder.string(command.functionName())),
                Map.entry("args", Encoder.object(command.parameters)),
                Map.entry("trace_id", Encoder.string(command.traceId)),
                Map.entry("expires_at", Encoder.number(expiresAt()))
        );

        PutItemRequest request = PutItemRequest.builder()
                .tableName(TABLE_TRANSACTION_QUEUE)
                .item(attributes)
                .conditionExpression("attribute_not_exists(request_id)")
                .build();

        try {
            this.dynamoDbClient.putItem(request);
            log.info("Succeed to register send transaction to dynamodb. requestId : {}, contractName : {}, method: {}",
                     command.requestId, command.contractName(), command.functionName());
        } catch (ConditionalCheckFailedException failedExc) {
            String message = "RequestId is duplicated. requestId = " + command.requestId
                    + ", zoneId = " + command.zoneId.getValue() + ", contractName = " + command.contractName()
                    + ", method = " + command.functionName();

            throw new RequestIdDuplicatedException(message, failedExc);
        } catch (DynamoDbException exc) {
            log.error("Failed to register send transaction to dynamodb. requestId : {}, contractName : {}, method: {}",
                      command.requestId, command.contractName(), command.functionName(), exc);

            throw exc;
        }
    }

    private static String expiresAt() {
        long epochSecond = Instant.now().plus(EXPIRATION_DAYS, ChronoUnit.DAYS).getEpochSecond();
        return Long.toString(epochSecond);
    }

    /**
     * send transaction の依頼が失敗した際に状態を queueing_failed に更新する。
     *
     * @param requestId リクエストID
     */
    public void failed(String requestId) {
        Map<String, AttributeValue> key = Map.ofEntries(
                Map.entry("request_id", Encoder.string(requestId))
        );
        Map<String, AttributeValue> expressions = Map.ofEntries(
                Map.entry(":before_status", Encoder.string(TransactionStatus.QUEUING.getValue())),
                Map.entry(":next_status", Encoder.string(TransactionStatus.QUEUING_FAILED.getValue()))
        );

        UpdateItemRequest request = UpdateItemRequest.builder()
                .tableName(TABLE_TRANSACTION_QUEUE)
                .key(key)
                .updateExpression("SET transaction_status = :next_status")
                .conditionExpression("transaction_status = :before_status")
                .expressionAttributeValues(expressions)
                .build();

        try {
            this.dynamoDbClient.updateItem(request);
        } catch (ConditionalCheckFailedException failedExc) {
            log.error("Transaction is not queueing status. requestId = {}", requestId, failedExc);
        } catch (DynamoDbException exc) {
            log.error("Failed to update queueing failure status. requestId = {}", requestId, exc);
        }
    }

    /**
     * requestId を指定して send transaction を取得する。
     *
     * @param requestId リクエストID
     * @return send transaction
     */
    public TransactionValue findTransaction(String requestId) {
        Map<String, AttributeValue> key = Map.ofEntries(
                Map.entry("request_id", Encoder.string(requestId))
        );

        GetItemRequest request = GetItemRequest.builder()
                .key(key)
                .tableName(TABLE_TRANSACTION_QUEUE)
                .build();

        Map<String, AttributeValue> attributes;
        try {
            GetItemResponse response = this.dynamoDbClient.getItem(request);
            attributes = response.item();
        } catch (ResourceNotFoundException notFound) {
            throw notFound;
        } catch (DynamoDbException exc) {
            log.error("Failed to fetch transaction from queueing. requestId : {}", requestId, exc);
            throw exc;
        }

        if (attributes.isEmpty()) {
            return null;
        }

        return TransactionValue.builder()
                .requestId(attributes.get("request_id").s())
                .transactionStatus(TransactionStatus.of(attributes.get("transaction_status").s()))
                .zoneId(ZoneId.of(attributes.get("zone_id").n()))
                .contractName(attributes.get("contract_name").s())
                .method(attributes.get("method").s())
                .args(Decoder.object(attributes.get("args")))
                .traceId(Decoder.optionalString(attributes, "trace_id"))
                .nonceKey(Decoder.optionalString(attributes, "nonce_key"))
                .nonce(Decoder.optionalBigNumber(attributes, "nonce"))
                .sentAt(Decoder.optionalNumber(attributes, "sent_at"))
                .transactionHash(Decoder.optionalString(attributes, "transaction_hash"))
                .revertReason(Decoder.optionalString(attributes, "revert_reason"))
                .inSending(attributes.containsKey("in_sending"))
                .build();
    }

    public boolean existsTransactionWithHash(String transactionHash) {
        Map<String, AttributeValue> attributes = Map.ofEntries(
                Map.entry(":v_hash", AttributeValue.builder().s(transactionHash).build())
        );
        QueryRequest queryRequest = QueryRequest.builder()
                .tableName(TABLE_TRANSACTION_QUEUE)
                .indexName(this.dynamoDbProperty.transactionQueueSecondaryIndex) // セカンダリインデクスの名前を指定します
                .keyConditionExpression("transaction_hash = :v_hash")
                .expressionAttributeValues(attributes)
                .build();

        try {
            QueryResponse queryResponse = this.dynamoDbClient.query(queryRequest);
            List<Map<String, AttributeValue>> items = queryResponse.items();

            // マッチする項目があれば、その項目が存在すると判断します
            return items.isEmpty() == false;
        } catch (RuntimeException exc) {
            log.warn("Failed to query from transaction_queue. {}", exc.getMessage(), exc);
            return false;
        }
    }

    /**
     * 指定した send transaction の nonce 値を設定し、送信前状態に更新する。
     *
     * @param requestId リクエストID
     * @param nonceKey nonce 定義
     * @param nonce nonce 値
     * @param beforeStatus 対象となるレコードの変更前の状態
     */
    public void prepareSending(
            String requestId, String nonceKey, BigInteger nonce, TransactionStatus beforeStatus
    ) {
        Map<String, AttributeValue> key = Map.ofEntries(Map.entry("request_id", Encoder.string(requestId)));
        Map<String, AttributeValue> expressions = Map.ofEntries(
                Map.entry(":before_status", Encoder.string(beforeStatus.getValue())),
                Map.entry(":next_status", Encoder.string(TransactionStatus.BEFORE_SENT.getValue())),
                Map.entry(":nonce_key", Encoder.string(nonceKey)),
                Map.entry(":nonce", Encoder.number(nonce.toString()))
        );

        UpdateItemRequest request = UpdateItemRequest.builder()
                .tableName(TABLE_TRANSACTION_QUEUE)
                .key(key)
                .updateExpression("SET transaction_status = :next_status , nonce_key = :nonce_key , nonce = :nonce")
                .conditionExpression("transaction_status = :before_status")
                .expressionAttributeValues(expressions)
                .build();

        try {
            this.dynamoDbClient.updateItem(request);
        } catch (ConditionalCheckFailedException failedExc) {
            log.error("Transaction is invalid status. requestId = {}, nonce = {}", requestId, nonce, failedExc);
            throw failedExc;
        } catch (DynamoDbException exc) {
            log.error("Failed to update preparing sending status. requestId = {}, nonce = {}", requestId, nonce, exc);
            throw exc;
        }
    }

    /**
     * 指定した send transaction を送信済み状態に更新し、transactionHash を記録する。
     *
     * @param requestId リクエストID
     * @param transactionHash トランザクションハッシュ値
     */
    public void recordTransactionHash(String requestId, String transactionHash) {
        Map<String, AttributeValue> key = Map.ofEntries(Map.entry("request_id", Encoder.string(requestId)));
        Map<String, AttributeValueUpdate> attributes = Map.ofEntries(
                Map.entry("transaction_status", AttributeValueUpdate.builder()
                        .value(Encoder.string(TransactionStatus.SENT.getValue()))
                        .action(AttributeAction.PUT)
                        .build()),
                Map.entry("transaction_hash", AttributeValueUpdate.builder()
                        .value(Encoder.string(transactionHash))
                        .action(AttributeAction.PUT).build()),
                Map.entry("in_sending", AttributeValueUpdate.builder()
                        .value(Encoder.number("1"))
                        .action(AttributeAction.PUT).build())
        );

        UpdateItemRequest request = UpdateItemRequest.builder()
                .tableName(TABLE_TRANSACTION_QUEUE)
                .key(key)
                .attributeUpdates(attributes)
                .build();

        this.dynamoDbClient.updateItem(request);
    }

    /**
     * 指定された transaction_hash の処理結果を保存する。
     *
     * @param transactionHash トランザクションハッシュ値
     * @param status 状態
     * @return 保存できた場合にtrue
     */
    public boolean completeTransactionStatus(String transactionHash, TransactionStatus status, String revertReason) {
        List<Map.Entry<String, AttributeValue>> entries = new ArrayList<>(3);
        entries.add(Map.entry("transaction_hash", Encoder.string(transactionHash)));
        entries.add(Map.entry("transaction_status", Encoder.string(status.getValue())));
        if (revertReason != null) {
            entries.add(Map.entry("revert_reason", Encoder.string(revertReason)));
        }

        Map<String, AttributeValue> attributes = entries.stream().collect(Collectors.toMap(
                entry -> entry.getKey(),
                entry -> entry.getValue()
        ));

        PutItemRequest request = PutItemRequest.builder()
                .tableName(TABLE_TRANSACTION_RESULT)
                .item(attributes)
                .conditionExpression("attribute_not_exists(transaction_hash)")
                .build();

        try {
            this.dynamoDbClient.putItem(request);
            log.info("Succeed to record send transaction status to dynamodb."
                             + " transactionHash : {}, transactionStatus : {}",
                     transactionHash, status.getValue());

            return true;
        } catch (ConditionalCheckFailedException failedExc) {
            return false;
        } catch (DynamoDbException exc) {
            log.error(
                    "Failed to record send transaction status to dynamodb. transactionHash : {}, transactionStatus : {}",
                    transactionHash, status.getValue(), exc);
            throw exc;
        }
    }

    public TransactionResultValue findTransactionResult(String transactionHash) {
        Map<String, AttributeValue> key = Map.ofEntries(
                Map.entry("transaction_hash", Encoder.string(transactionHash))
        );

        GetItemRequest request = GetItemRequest.builder()
                .key(key)
                .tableName(TABLE_TRANSACTION_RESULT)
                .build();

        Map<String, AttributeValue> attributes;
        try {
            GetItemResponse response = this.dynamoDbClient.getItem(request);
            attributes = response.item();
        } catch (ResourceNotFoundException notFound) {
            log.debug("Transaction is not found in result. transactionHash : {}", transactionHash, notFound);
            return null;
        } catch (DynamoDbException exc) {
            log.error("Failed to fetch transaction from result. transactionHash : {}", transactionHash, exc);
            throw exc;
        }

        if (attributes.isEmpty()) {
            return null;
        }

        return TransactionResultValue.builder()
                .transactionHash(transactionHash)
                .transactionStatus(TransactionStatus.of(attributes.get("transaction_status").s()))
                .revertReason(Decoder.optionalString(attributes, "revert_reason"))
                .build();
    }

    public long fetchSendingCounter() {
        GetItemRequest request = GetItemRequest.builder()
                .key(KEY_SENDING_COUNTER)
                .tableName(TABLE_SENDING_COUNTER)
                .build();

        Map<String, AttributeValue> attributes;
        try {
            GetItemResponse response = this.dynamoDbClient.getItem(request);
            attributes = response.item();
        } catch (ResourceNotFoundException notFound) {
            return 0L;
        } catch (DynamoDbException exc) {
            log.error("Failed to fetch sending counter.", exc);
            throw exc;
        }

        Long value = Decoder.optionalNumber(attributes, "counter", Long.valueOf(0L));
        return value.longValue();
    }

    /**
     * トランザクション送信件数管理テーブルの件数を増分する。
     */
    public void incrementSendingCounter() {
        this.doIncrementSendingCounter(1);
    }

    /**
     * トランザクション送信件数管理テーブルの件数を減分する。
     */
    public void decrementSendingCounter() {
        this.doIncrementSendingCounter(-1);
    }

    private void doIncrementSendingCounter(int delta) {
        Map<String, AttributeValue> expressions = Map.ofEntries(
                Map.entry(":value", Encoder.number(Integer.toString(delta)))
        );
        Map<String, String> attributeNames = Map.ofEntries(Map.entry("#sending_counter", "counter"));
        UpdateItemRequest request = UpdateItemRequest.builder()
                .tableName(TABLE_SENDING_COUNTER)
                .key(KEY_SENDING_COUNTER)
                .updateExpression("ADD #sending_counter :value")
                .expressionAttributeNames(attributeNames)
                .expressionAttributeValues(expressions)
                .build();

        try {
            this.dynamoDbClient.updateItem(request);
        } catch (DynamoDbException exc) {
            log.error("Failed to update sending counter. delta = {}", Integer.valueOf(delta), exc);
        }
    }

    private static class Encoder {

        private static AttributeValue dispatch(Object value) {
            if (value instanceof List<?> list) {
                return AttributeValue.builder().l(list.stream().map(obj -> dispatch(obj)).toList()).build();
            }

            if (value instanceof Map<?, ?> map) {
                return object(map);
            }

            return string(value.toString());
        }

        private static AttributeValue string(String value) {
            return AttributeValue.builder().s(value).build();
        }

        private static AttributeValue number(String value) {
            return AttributeValue.builder().n(value).build();
        }

        private static AttributeValue object(Map<?, ?> map) {
            Map<String, AttributeValue> attributes = map.entrySet().stream()
                    .collect(
                            Collectors.toMap(
                                    entry -> entry.getKey().toString(),
                                    entry -> dispatch(entry.getValue())
                            )
                    );

            return AttributeValue.builder().m(attributes).build();
        }
    }

    private static class Decoder {

        private static String optionalString(Map<String, AttributeValue> attributes, String key) {
            if (attributes.containsKey(key) == false) {
                return null;
            }

            return attributes.get(key).s();
        }

        private static Long optionalNumber(Map<String, AttributeValue> attributes, String key) {
            return optionalNumber(attributes, key, null);
        }

        private static Long optionalNumber(Map<String, AttributeValue> attributes, String key, Long defaultValue) {
            if (attributes.containsKey(key) == false) {
                return defaultValue;
            }

            String number = attributes.get(key).n();
            return Long.valueOf(number);
        }

        private static BigInteger optionalBigNumber(Map<String, AttributeValue> attributes, String key) {
            if (attributes.containsKey(key) == false) {
                return null;
            }

            String number = attributes.get(key).n();
            return new BigInteger(number);
        }

        private static Object dispatch(AttributeValue attribute) {
            if (attribute.hasL()) {
                return attribute.l().stream().map(item -> dispatch(item)).toList();
            }

            if (attribute.hasM()) {
                return object(attribute);
            }

            String stringValue = attribute.s();
            if (stringValue != null) {
                return stringValue;
            }

            String numberValue = attribute.n();
            if (numberValue != null) {
                return numberValue;
            }

            throw new UnsupportedOperationException("Not implemented attribute : " + attribute);
        }

        private static Map<String, Object> object(AttributeValue attribute) {
            return attribute.m().entrySet().stream()
                    .collect(Collectors.toMap(
                            entry -> entry.getKey(),
                            entry -> dispatch(entry.getValue())
                    ));
        }
    }
}

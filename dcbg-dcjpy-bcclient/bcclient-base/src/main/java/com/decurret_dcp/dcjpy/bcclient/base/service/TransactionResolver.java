package com.decurret_dcp.dcjpy.bcclient.base.service;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractAbi;
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractFunction;
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractInfo;
import com.decurret_dcp.dcjpy.bcclient.base.exception.BadRequestException;
import com.decurret_dcp.dcjpy.bcclient.base.exception.ContractNotFoundException;
import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthTransactionCommand;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Component
@Slf4j
public class TransactionResolver {

    private final ContractInfo contractInfo;

    private final ExternalAbiResolver externalAbiResolver;

    public EthTransactionCommand resolve(TransactionCommand command) {
        if (this.contractInfo.hasContractApi(command.zoneId, command.contractName) == false) {
            return this.resolveFromExternal(command);
        }

        ContractAbi contractAbi = this.contractInfo.getContractAbi(command.zoneId, command.contractName);
        if (contractAbi.hasFunction(command.method) == false) {
            return this.resolveFromExternal(command);
        }

        ContractFunction contractFunction = contractAbi.getFunction(command.method);

        return EthTransactionCommand.builder()
                .requestId(command.requestId)
                .zoneId(command.zoneId)
                .contractAbi(contractAbi)
                .contractFunction(contractFunction)
                .parameters(command.args)
                .traceId(StringUtils.hasText(command.traceId) ? command.traceId : "")
                .build();
    }

    private EthTransactionCommand resolveFromExternal(TransactionCommand command) {
        log.debug("No matching internal contracts. contractName : {}, method : {}",
                  command.contractName, command.method);

        try {
            ContractAbi contractAbi = this.externalAbiResolver.resolve(command.zoneId, command.contractName);
            ContractFunction contractFunction = contractAbi.getFunction(command.method);

            return EthTransactionCommand.builder()
                    .requestId(command.requestId)
                    .zoneId(command.zoneId)
                    .contractAbi(contractAbi)
                    .contractFunction(contractFunction)
                    .parameters(command.args)
                    .traceId(StringUtils.hasText(command.traceId) ? command.traceId : "")
                    .build();
        } catch (ContractNotFoundException notFound) {
            throw new BadRequestException("contractName not found", notFound);
        }
    }
}

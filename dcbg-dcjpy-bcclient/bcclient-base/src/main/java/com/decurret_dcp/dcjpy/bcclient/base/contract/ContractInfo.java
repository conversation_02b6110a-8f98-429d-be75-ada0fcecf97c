package com.decurret_dcp.dcjpy.bcclient.base.contract;

import com.decurret_dcp.dcjpy.bcclient.base.exception.AbiParseException;
import com.decurret_dcp.dcjpy.bcclient.base.exception.ContractNotFoundException;
import com.decurret_dcp.dcjpy.bcclient.base.properties.AbiFormat;
import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty;
import com.decurret_dcp.dcjpy.bcclient.base.properties.S3Property;
import com.decurret_dcp.dcjpy.bcclient.base.adaptor.AwsS3Adaptor;
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId;

import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class ContractInfo {

    private static final Pattern ABI_FILENAME_PATTERN = Pattern.compile("(?:.*/)?(3[0-9]{3})/([^/]+)\\.json");

    private static final int ERROR_EXIT_CODE = 1;

    private final Map<ZoneId, Map<String, ContractAbi>> contractMap;

    public ContractInfo(
            ApplicationContext applicationContext,
            AwsS3Adaptor s3Adaptor,
            ApplicationProperty properties,
            S3Property s3Property
    ) {
        log.info("Start loading contracts ...");
        try {
            String bucketName = s3Property.contractBucketName;
            this.contractMap = Collections.unmodifiableMap(
                    initContractMapping(s3Adaptor, bucketName, properties.getAbiFormat())
            );
        } catch (RuntimeException exc) {
            log.error("ABI file reading error. Stopping application ...", exc);
            SpringApplication.exit(applicationContext, () -> ERROR_EXIT_CODE);
            throw exc;
        }

        log.info("Loading contracts finished.");
    }

    private static Map<ZoneId, Map<String, ContractAbi>> initContractMapping(
            AwsS3Adaptor s3Adaptor, String bucketName, AbiFormat abiFormat
    ) {
        // ".json" 拡張子以外のファイルを除外して取得
        Map<String, byte[]> abiContents = s3Adaptor.fetchAbiContents(bucketName, (key) -> key.endsWith(".json"));

        // JSON内容をクラスにマッピングする
        Map<ZoneId, Map<String, ContractAbi>> contractMap = new HashMap<>();
        abiContents.forEach((String key, byte[] content) -> {
            Matcher matcher = ABI_FILENAME_PATTERN.matcher(key);
            if (matcher.find() == false) {
                log.warn("Not matched file path. key : {}", key);
                return;
            }

            ZoneId zoneId = ZoneId.of(matcher.group(1));
            String contractName = matcher.group(2);

            Map<String, ContractAbi> map = contractMap.computeIfAbsent(zoneId, value -> new HashMap<>());

            try {
                ContractAbi abi = ContractAbiConvertUtil.convertByteToObject(abiFormat, contractName, content);
                map.put(abi.getName(), abi);
            } catch (Exception exc) {
                log.error("Failed to parse ABI file ({}).", key);
                throw new AbiParseException("ABI file parsing failed. abi file : " + key, exc);
            }
        });

        return contractMap;
    }

    public boolean hasContractApi(ZoneId zoneId, String contractName) {
        return this.contractMap.getOrDefault(zoneId, Map.of()).containsKey(contractName);
    }

    public ContractAbi getContractAbi(ZoneId zoneId, String contractName) throws ContractNotFoundException {
        if (this.hasContractApi(zoneId, contractName) == false) {
            throw new ContractNotFoundException("contractName not found");
        }

        return this.contractMap.getOrDefault(zoneId, Map.of()).get(contractName);
    }
}

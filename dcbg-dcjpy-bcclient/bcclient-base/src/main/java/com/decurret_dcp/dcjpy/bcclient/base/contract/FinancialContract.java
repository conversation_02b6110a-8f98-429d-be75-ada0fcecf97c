package com.decurret_dcp.dcjpy.bcclient.base.contract;

import java.util.Set;

/**
 * FinDLT に Call リクエストを送信する必要があるコントラクトを管理する。
 */
public class FinancialContract {

    private static final Set<String> FINANCIAL_CONTRACTS = Set.of("FinancialCheck", "FinancialZoneAccount", "Issuer");

    /**
     * BizZone であっても Fin DLT に Call が必要なコントラクト名か判定する。
     *
     * @param contractName コントラクト名
     * @return 含まれている場合: true
     */
    public static boolean mustFinancial(String contractName) {
        return FINANCIAL_CONTRACTS.contains(contractName);
    }
}

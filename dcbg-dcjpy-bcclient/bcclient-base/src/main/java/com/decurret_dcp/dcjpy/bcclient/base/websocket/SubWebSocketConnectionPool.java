package com.decurret_dcp.dcjpy.bcclient.base.websocket;

import lombok.extern.slf4j.Slf4j;

import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty;

@Slf4j
public class SubWebSocketConnectionPool extends WebSocketConnectionPoolBase {

    public SubWebSocketConnectionPool(ApplicationProperty property) {
        super(property.getSubWebSocketUriHost(), property.getSubWebSocketUriPort(),
              false, BcEventHandler.NO_ACTION);
    }

    @Override
    public void createWebSocketConnection() {
        super.createWebSocketConnection();
        log.info("Sub WebSocket connection pool is created.");
    }
}

package com.decurret_dcp.dcjpy.bcclient.base.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;
import org.springframework.util.StringUtils;

import lombok.AllArgsConstructor;
import lombok.ToString;

@ConstructorBinding
@ConfigurationProperties(prefix = "dynamodb")
@AllArgsConstructor
@ToString
public class DynamoDbProperty {

    /** ローカル環境への接続先。 */
    public final String localEndpoint;

    /** transaction_queueテーブルのセカンダリインデクス名**/
    public final String transactionQueueSecondaryIndex;

    /**
     * AWS 環境の DynamoDB に接続するかどうか。
     *
     * @return AWS 環境の DynamoDB に接続する場合 true
     */
    public boolean toAwsConnection() {
        return (StringUtils.hasText(this.localEndpoint) == false);
    }
}

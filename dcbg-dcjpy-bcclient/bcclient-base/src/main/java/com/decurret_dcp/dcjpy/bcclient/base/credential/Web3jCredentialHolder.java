package com.decurret_dcp.dcjpy.bcclient.base.credential;

import com.decurret_dcp.dcjpy.bcclient.base.util.Web3jHashUtil;
import org.web3j.crypto.Credentials;

public class Web3jCredentialHolder {

    private final Credentials credentials;

    public Web3jCredentialHolder() {
        this.credentials = Web3jHashUtil.generateCredentials();
    }

    /**
     * Credentialsを取得する
     *
     * @return Credentials
     */
    public Credentials getCredentials() {
        return this.credentials;
    }
}

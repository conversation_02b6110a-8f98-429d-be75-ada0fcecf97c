package com.decurret_dcp.dcjpy.bcclient.base.contract;

import org.web3j.abi.datatypes.Type;

/**
 * Java 標準のオブジェクトを web3j が定義する型への変換処理を表す。
 * 
 * @param <TYPE_CLASS> web3j の型
 * @param <FROM_OBJECT> 変換元の型
 */
public interface ToAbiType<TYPE_CLASS extends Type<?>, FROM_OBJECT> {
    
    public boolean isDynamic();

    /**
     * web3j の Type インタフェースを実装する Class クラスを取得する。
     * このメソッドは DynamicArray もしくは StaticArray の要素の型を特定するために利用する。
     * 
     * @return Type インタフェースを実装する Class クラス
     */
    public Class<TYPE_CLASS> getTypeClass();

    /**
     * 引数の値を web3j が利用できるデータ型に変換する。
     * 
     * @param value 変換前の値
     * @return 変換後の値
     */
    public Type<?> toAbiType(FROM_OBJECT value);
}

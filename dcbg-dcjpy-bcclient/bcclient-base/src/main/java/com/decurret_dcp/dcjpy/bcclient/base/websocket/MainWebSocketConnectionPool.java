package com.decurret_dcp.dcjpy.bcclient.base.websocket;

import lombok.extern.slf4j.Slf4j;

import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty;

@Slf4j
public class MainWebSocketConnectionPool extends WebSocketConnectionPoolBase {

    public MainWebSocketConnectionPool(ApplicationProperty property, BcEventHandler handler) {
        super(property.getWebSocketUriHost(), property.getWebSocketUriPort(), false, handler);
    }

    @Override
    public void createWebSocketConnection() {
        super.createWebSocketConnection();
        log.info("Main websocket connection pool is created.");
    }
}

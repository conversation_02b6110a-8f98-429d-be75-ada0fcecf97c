package com.decurret_dcp.dcjpy.bcclient.base.contract;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.web3j.abi.datatypes.DynamicStruct;
import org.web3j.abi.datatypes.StaticStruct;
import org.web3j.abi.datatypes.Type;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@EqualsAndHashCode
@ToString
@SuppressWarnings({ "rawtypes", "unchecked" })
public class ContractInputParameter {

    private static final Pattern ARRAY_SUFFIX = Pattern.compile("\\[(\\d*)]");

    private final String name;
    private final String solidityType;
    private final ToAbiType converter;
    private final List<ContractInputParameter> components;

    private ContractInputParameter(String name, String solidityType, ToAbiType<?, Object> converter) {
        this(name, solidityType, converter, List.of());
    }

    private ContractInputParameter(String name, String solidityType, ToAbiType<?, Object> converter,
            List<ContractInputParameter> components) {

        this.name = name;
        this.solidityType = solidityType;
        this.converter = converter;
        this.components = List.copyOf(components);
    }

    public Type<?> toAbiValue(Object value) {
        return this.converter.toAbiType(value);
    }

    public boolean isDynamic() {
        return this.converter.isDynamic();
    }

    /**
     * tuple 型(および tuple 型の配列)以外の ContractInputParameter を生成する。
     * 
     * @param paramName パタメータ名
     * @param solidityType パラメータの型名
     * @return ContractInputParameter オブジェクト
     */
    public static ContractInputParameter create(String paramName, String solidityType) {
        int indexOfBracket = solidityType.indexOf("[");
        String baseSolidityType = (indexOfBracket >= 0) ? solidityType.substring(0, indexOfBracket) : solidityType;
        AbiTypeConverter<?, Object> baseConverter = AbiTypeConverter.getConverter(baseSolidityType);

        ToAbiType<?, Object> converter = wrapArrayConveter(solidityType, baseConverter);

        return new ContractInputParameter(paramName, solidityType, converter);
    }

    /**
     * tuple 型の ContractInputParameter を生成する。
     * 
     * @param paramName パタメータ名
     * @param solidityType パラメータの型名
     * @param components 構造体の各要素
     * @return ContractInputParameter オブジェクト
     */
    public static ContractInputParameter create(String paramName, String solidityType,
            List<ContractInputParameter> components) {

        final boolean isDynamic = components.stream().anyMatch((input) -> input.isDynamic());
        ToAbiType<?, Object> tupleConverter = isDynamic ? initDynamicConverter(components)
                : initStaticConverter(components);

        ToAbiType<?, Object> converter = wrapArrayConveter(solidityType, tupleConverter);

        return new ContractInputParameter(paramName, solidityType, converter, components);
    }

    private static ToAbiType<?, Object> initDynamicConverter(List<ContractInputParameter> components) {
        ToAbiType<?, ?> toAbiType = new ToAbiType<DynamicStruct, Map<String, Object>>() {

            @Override
            public boolean isDynamic() {
                return true;
            }

            @Override
            public Class<DynamicStruct> getTypeClass() {
                return DynamicStruct.class;
            }

            @Override
            public Type<List<Type>> toAbiType(Map<String, Object> value) {
                List<Type> convertedValues = components.stream()
                        .map((component) -> component.toAbiValue(value.get(component.getName())))
                        .collect(Collectors.toList());
                return new DynamicStruct(convertedValues);
            }
        };

        return (ToAbiType<?, Object>) toAbiType;
    }

    private static ToAbiType<?, Object> initStaticConverter(List<ContractInputParameter> components) {
        ToAbiType<?, ?> toAbiType = new ToAbiType<StaticStruct, Map<String, Object>>() {

            @Override
            public boolean isDynamic() {
                return false;
            }

            @Override
            public Class<StaticStruct> getTypeClass() {
                return StaticStruct.class;
            }

            @Override
            public Type<List<Type>> toAbiType(Map<String, Object> value) {
                List<Type> convertedValues = components.stream()
                        .map((component) -> component.toAbiValue(value.get(component.getName())))
                        .collect(Collectors.toList());
                return new StaticStruct(convertedValues);
            }
        };

        return (ToAbiType<?, Object>) toAbiType;
    }

    private static ToAbiType<?, Object> wrapArrayConveter(String solidityType, ToAbiType<?, Object> fromConverter) {
        // reference from org.web3j.abi.TypeReference.makeTypeReference(String, boolean, boolean)

        Matcher nextSquareBrackets = ARRAY_SUFFIX.matcher(solidityType);
        if (nextSquareBrackets.find() == false) {
            return fromConverter;
        }

        ToAbiType<?, ?> converter = fromConverter;
        int lastReadStringPosition = nextSquareBrackets.start();
        final int len = solidityType.length();

        // for each [\d*], wrap the previous TypeReference in an array
        while (lastReadStringPosition < len) {
            ToAbiType<?, ?> baseConverter = converter;
            String arraySize = nextSquareBrackets.group(1);
            if ((arraySize == null) || arraySize.equals("")) {
                converter = AbiTypeConverter.dynamicArrayConverter(baseConverter);
            } else {
                int size = Integer.parseInt(arraySize);
                converter = AbiTypeConverter.staticArrayConverter(baseConverter, size);
            }

            lastReadStringPosition = nextSquareBrackets.end();
            nextSquareBrackets = ARRAY_SUFFIX.matcher(solidityType);
            // cant find any more [] and string isn't fully parsed
            if ((nextSquareBrackets.find(lastReadStringPosition) == false) && (lastReadStringPosition != len)) {
                throw new IllegalStateException("Unable to create api converter from " + solidityType);
            }
        }

        return (ToAbiType<?, Object>) converter;
    }
}

package com.decurret_dcp.dcjpy.bcclient.base.controller;

import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.SubWebSocketConnectionPool;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class CustomHealthIndicator implements HealthIndicator {

    private final ApplicationProperty property;

    private final MainWebSocketConnectionPool mainWebSocketConnectionPool;

    private final SubWebSocketConnectionPool subWebSocketConnectionPool;

    public CustomHealthIndicator(
            ApplicationProperty property, MainWebSocketConnectionPool mainWebSocketConnectionPool
    ) {
        this(property, mainWebSocketConnectionPool, null);
    }

    /**
     * メイン領域とサブ領域のWebSocket接続状態を確認し少なくとも一方が接続されていない場合にDownを返却する
     *
     * @return Health
     */
    @Override
    public Health health() {
        if (this.mainWebSocketConnectionPool.isNewHeadsSubscriptionDisposed()) {
            return Health.down().build();
        }

        if (this.subWebSocketConnectionPool == null) {
            return Health.up().build();
        }

        boolean useSubWebSocket = this.property.isUseSubWebSocket();
        if (useSubWebSocket && this.subWebSocketConnectionPool.isNewHeadsSubscriptionDisposed()) {
            return Health.down().build();
        }

        return Health.up().build();
    }
}

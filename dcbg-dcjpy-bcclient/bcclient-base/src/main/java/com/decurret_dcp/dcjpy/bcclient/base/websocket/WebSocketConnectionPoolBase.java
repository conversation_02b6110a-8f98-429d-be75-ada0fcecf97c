package com.decurret_dcp.dcjpy.bcclient.base.websocket;

import java.io.IOException;
import java.math.BigInteger;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import javax.annotation.PostConstruct;

import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;

import com.decurret_dcp.dcjpy.bcclient.base.exception.BlockchainIOException;
import com.decurret_dcp.dcjpy.bcclient.base.util.WebSocketUtil;

import lombok.extern.slf4j.Slf4j;

import org.java_websocket.exceptions.WebsocketNotConnectedException;
import org.springframework.scheduling.annotation.Scheduled;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.methods.response.EthBlock;
import org.web3j.protocol.websocket.WebSocketService;
import org.web3j.protocol.websocket.events.NewHead;
import org.web3j.protocol.websocket.events.NewHeadsNotification;
import org.web3j.utils.Numeric;

@Slf4j
public abstract class WebSocketConnectionPoolBase {

    private final ReadWriteLock lock;

    private final String host;

    private final String port;

    private final boolean useSecureConnection;

    private final BcEventHandler handler;

    private Web3j callerWeb3j;

    private Disposable newHeadsSubscription;

    private long lastLoggedAt;

    protected WebSocketConnectionPoolBase(
            String host, String port, boolean useSecureConnection, BcEventHandler handler
    ) {
        this.lock = new ReentrantReadWriteLock();
        this.host = host;
        this.port = port;
        this.useSecureConnection = useSecureConnection;
        this.handler = handler;
        this.lastLoggedAt = 0L;
    }

    @PostConstruct
    public void initialize() {
        this.createWebSocketConnection();
    }

    /**
     * プール用WebSocket接続を作成する
     */
    public void createWebSocketConnection() {
        Lock lock = this.lock.writeLock();
        try {
            lock.lock();
            this.doCreateWebSocketConnection();
        } finally {
            lock.unlock();
        }
    }

    private void doCreateWebSocketConnection() {
        if (this.callerWeb3j != null) {
            this.callerWeb3j.shutdown();
        }

        WebSocketService callerWebSocket =
                WebSocketUtil.generateWebSocketService(this.host, this.port, this.useSecureConnection);
        this.callerWeb3j = Web3j.build(callerWebSocket);
    }

    /**
     * プールしたWebSocket接続を取得する
     *
     * @return WebSocketService
     */
    public Web3j getWebSocketConnection() {
        Lock lock = this.lock.readLock();
        try {
            lock.lock();

            if (this.callerWeb3j == null) {
                log.error("WebSocket is not initialized.");
                throw new IllegalStateException("WebSocket is not initialized.");
            }

            return this.callerWeb3j;
        } finally {
            lock.unlock();
        }
    }

    @Scheduled(fixedDelayString = "${app.subscriptionCheckInterval}")
    public void checkSubscription() {
        Lock lock = this.lock.writeLock();
        try {
            lock.lock();

            if ((this.newHeadsSubscription == null) || this.newHeadsSubscription.isDisposed()) {
                this.startSubscription();
                return;
            }

            long currentTime = System.currentTimeMillis();
            if (currentTime - this.lastLoggedAt <= TimeUnit.HOURS.toMillis(1L)) {
                return;
            }

            log.debug("DLT event subscription is alive : {}", this.getClass().getSimpleName());
            this.lastLoggedAt = currentTime;
        } finally {
            lock.unlock();
        }
    }

    private void startSubscription() {
        this.doCreateWebSocketConnection();

        WebSocketService listenerWebSocket =
                WebSocketUtil.generateWebSocketService(this.host, this.port, this.useSecureConnection);
        Web3j listenerWeb3j = Web3j.build(listenerWebSocket);

        Flowable<NewHeadsNotification> flowable = listenerWeb3j.newHeadsNotifications();
        this.newHeadsSubscription = flowable.subscribe(
                // onNext
                notification -> this.doSubscribe(notification),
                // onError : エラーが発生すれば、subscribe 処理は中断される
                (error) -> {
                    log.error("An error occurred in the process of listening DLT events.", error);
                    if (error instanceof WebsocketNotConnectedException) {
                        // TODO
                        this.createWebSocketConnection();
                    }
                }
        );
    }

    private void doSubscribe(NewHeadsNotification notification) {
        NewHead newHead = notification.getParams().getResult();
        BigInteger blockNumber = Numeric.toBigInt(newHead.getNumber());
        DefaultBlockParameter parameter = DefaultBlockParameter.valueOf(blockNumber);

        // ブロックデータ取得
        EthBlock ethBlock;
        try {
            ethBlock = this.callerWeb3j.ethGetBlockByNumber(parameter, true).send();
        } catch (IOException ioExc) {
            log.error("GetBlockByNumber failed.", ioExc);
            throw new BlockchainIOException("ethGetBlockByNumber.send() failed", ioExc);
        }

        this.handler.onEvent(this, ethBlock);
    }

    /**
     * NewHeads の購読が破棄されているか
     *
     * @return 購読が存在しないか破棄されている場合 true
     */
    public boolean isNewHeadsSubscriptionDisposed() {
        Lock lock = this.lock.readLock();
        try {
            lock.lock();

            return (this.newHeadsSubscription == null) || this.newHeadsSubscription.isDisposed();
        } finally {
            lock.unlock();
        }
    }
}

package com.decurret_dcp.dcjpy.bcclient.base.properties;

public enum AbiFormat {

    /** HardHat */
    HARDHAT,

    /** Truffle */
    TRUFFLE;

    public static AbiFormat get(String format) {
        // デフォルト値は HardHat とする
        if (format == null) {
            return HARDHAT;
        }

        for (AbiFormat abiFormat : AbiFormat.values()) {
            if (abiFormat.name().equalsIgnoreCase(format)) {
                return abiFormat;
            }
        }

        return HARDHAT;
    }
}

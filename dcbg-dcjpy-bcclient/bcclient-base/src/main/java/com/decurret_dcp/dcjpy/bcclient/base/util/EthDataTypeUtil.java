package com.decurret_dcp.dcjpy.bcclient.base.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.lang.NonNull;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.primitive.PrimitiveType;

import javax.xml.bind.DatatypeConverter;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;

public class EthDataTypeUtil {
    private static final String ETH_TYPE_SUFFIX_ARRAY = "[]";

    private static final String CHARSET = "US-ASCII";
    private static final String HEX_NUMBER_PREFIX = "0x";

    /**
     * 型文字列をAbiTypesのクラスに変換する
     *
     * @param ethTypeString 型文字列
     * @return AbiTypesクラス
     */
    @NonNull
    public static Class toTypeClass(@NonNull String ethTypeString) {
        if (ethTypeString == null || ethTypeString.isBlank()) {
            throw new IllegalArgumentException("ethTypeString is null or empty.");
        }
        if (isArray(ethTypeString)) {
            return DynamicArray.class;
        }
        return AbiTypes.getType(ethTypeString);
    }

    /**
     * 指定された型文字列をTypeReferenceに変換する
     *
     * @param ethTypeString 型文字列
     * @return TypeReference
     */
    @NonNull
    public static TypeReference toTypeReference(@NonNull String ethTypeString) {
        try {
            return TypeReference.makeTypeReference(ethTypeString);
        } catch (ClassNotFoundException e) {
            throw new IllegalArgumentException("Unsupported type reference. type=" + ethTypeString, e);
        }
    }

    /**
     * AbiTypeの値を文字列に変換する
     *
     * @param type AbiType
     * @return 文字列
     */
    @NonNull
    public static String toStringValue(@NonNull Type type) {
        if (type instanceof BytesType) {
            // bytes, bytes1 ~ bytes32
            return HEX_NUMBER_PREFIX + new String(Hex.encodeHex((byte[]) type.getValue()));
        }
        if (type instanceof NumericType) {
            // int, uint, int8 ~ int256, uint8 ~ uint256,
            return String.valueOf(type.getValue());
        }
        if (type instanceof PrimitiveType) {
            if (type.getValue() instanceof byte[]) {
                // byte
                return new String((byte[]) type.getValue());
            } else {
                // short, long, int, float, double, char
                return String.valueOf(type.getValue());
            }
        }
        if (type instanceof Utf8String || type instanceof Address) {
            // string, address
            return String.valueOf(type);
        }
        if (type instanceof Bool) {
            // bool, boolean
            return String.valueOf(type.getValue());
        }
        throw new IllegalArgumentException("Unsupported AbiType");
    }

    /**
     * 配列要素の型文字列を取得する
     *
     * @param ethArrayTypeString 配列型文字列
     * @return 要素の型文字列
     */
    @NonNull
    public static String toBasicTypeString(@NonNull String ethArrayTypeString) {
        return ethArrayTypeString.substring(0, ethArrayTypeString.indexOf(ETH_TYPE_SUFFIX_ARRAY));
    }

    /**
     * 型文字列がBoolean型であるか判定する
     *
     * @param ethTypeString 型文字列
     * @return Boolean型である場合: true
     */
    @NonNull
    public static boolean isBool(@NonNull String ethTypeString) {
        String basicType = isArray(ethTypeString) ? toBasicTypeString(ethTypeString) : ethTypeString;
        return toTypeClass(basicType) == Bool.class;
    }

    /**
     * 型文字列が配列型であるか判定する
     *
     * @param ethTypeString 型文字列
     * @return 配列型である場合: true
     */
    @NonNull
    public static boolean isArray(@NonNull String ethTypeString) {
        return ethTypeString.contains(ETH_TYPE_SUFFIX_ARRAY);
    }

    /**
     * 文字列をバイト配列に変換する
     *
     * @param value 文字列
     * @return バイト配列
     * @throws UnsupportedEncodingException 文字コードが不正な場合
     */
    @NonNull
    private static byte[] stringToBytes(@NonNull String value) throws UnsupportedEncodingException {
        byte[] converted;
        if (value.startsWith(HEX_NUMBER_PREFIX)) {
            converted = DatatypeConverter.parseHexBinary(value.substring(2));
        } else {
            converted = Arrays.copyOf(value.getBytes(CHARSET), 32);
        }
        return converted;
    }
}

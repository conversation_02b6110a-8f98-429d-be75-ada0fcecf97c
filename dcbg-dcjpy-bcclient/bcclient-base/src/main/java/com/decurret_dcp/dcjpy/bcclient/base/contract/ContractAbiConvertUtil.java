package com.decurret_dcp.dcjpy.bcclient.base.contract;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.decurret_dcp.dcjpy.bcclient.base.properties.AbiFormat;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ContractAbiConvertUtil {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * バイト文字列で入ってきたJSONをContractABIに変換する
     *
     * @param in バイト文字列のJSON
     * @return ContractABI
     * @throws IOException JSON解析に失敗した場合
     */
    public static ContractAbi convertByteToObject(AbiFormat abiFormat, String contractName, byte[] in) throws IOException {
        JsonNode rootNode = OBJECT_MAPPER.readTree(in);

        String address;
        if (abiFormat == AbiFormat.HARDHAT) {
            address = rootNode.get("address").asText();
        } else if (abiFormat == AbiFormat.TRUFFLE) {
            // NOTE: Get address from the first chainId node.
            JsonNode networksNode = rootNode.get("networks");
            String chainId = networksNode.fieldNames().next();
            address = networksNode.get(chainId).get("address").asText();
        } else {
            throw new UnsupportedOperationException("AbiFormat is not supported : " + abiFormat);
        }

        JsonNode abi = rootNode.get("abi");

        List<ContractFunction> functionList = new ArrayList<>(abi.size());
        for (JsonNode functionInfoNode : abi) {
            JsonNode functionNameNode = functionInfoNode.get("name");
            // nameがないものはスキップ
            if (functionNameNode == null) {
                continue;
            }

            String functionName = functionInfoNode.get("name").asText();
            // Functionインプット変換
            List<ContractInputParameter> inputParamList = initInputParameter(functionInfoNode.get("inputs"));
            List<ContractOutputParameter<?>> outputParamList = initOutputParameter(functionInfoNode.get("outputs"));
            functionList.add(new ContractFunction(functionName, inputParamList, outputParamList));
        }

        log.info("contractName={}, address={}", contractName, address);
        return new ContractAbi(contractName, address, functionList);
    }

    /**
     * inputs ノードを ContractInputParameter の一覧に変換する。
     * 
     * @param inputNode ノード
     * @return ContractInputParameter の一覧
     */
    private static List<ContractInputParameter> initInputParameter(JsonNode inputNode) {
        if ((inputNode == null) || (inputNode.isEmpty())) {
            return List.of();
        }

        List<ContractInputParameter> inputParamList = new ArrayList<>();
        for (JsonNode node : inputNode) {
            String paramName = node.get("name").asText();
            String paramTypeString = node.get("type").asText();

            ContractInputParameter inputParam;
            // 構造体の場合。`startsWith` にしているのは、配列の場合、型の後に'[]'がつくため
            if (paramTypeString.startsWith("tuple")) {
                List<ContractInputParameter> components = initInputParameter(node.get("components"));
                inputParam = ContractInputParameter.create(paramName, paramTypeString, components);
            } else {
                inputParam = ContractInputParameter.create(paramName, paramTypeString);
            }

            inputParamList.add(inputParam);
        }

        return inputParamList;
    }

    private static List<ContractOutputParameter<?>> initOutputParameter(JsonNode inputNode) {
        if ((inputNode == null) || (inputNode.isEmpty())) {
            return List.of();
        }

        List<ContractOutputParameter<?>> outputParamList = new ArrayList<>();
        int index = 0;
        for (JsonNode node : inputNode) {
            String paramName = node.get("name").asText();
            if ((paramName == null) || paramName.isBlank()) {
                paramName = Integer.toString(index);
            }
            String paramTypeString = node.get("type").asText();

            ContractOutputParameter<?> outputParam;
            if (paramTypeString.startsWith("tuple")) {
                List<ContractOutputParameter<?>> components = initOutputParameter(node.get("components"));
                outputParam = ContractOutputParameter.create(paramName, paramTypeString, components);
            } else {
                outputParam = ContractOutputParameter.create(paramName, paramTypeString);
            }

            outputParamList.add(outputParam);
            index++;
        }

        return outputParamList;
    }
}

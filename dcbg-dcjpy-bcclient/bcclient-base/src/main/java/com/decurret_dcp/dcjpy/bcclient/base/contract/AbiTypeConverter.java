package com.decurret_dcp.dcjpy.bcclient.base.contract;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.xml.bind.DatatypeConverter;

import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Bool;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.abi.datatypes.DynamicBytes;
import org.web3j.abi.datatypes.Int;
import org.web3j.abi.datatypes.StaticArray;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Uint;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.*;
import org.web3j.abi.datatypes.primitive.Byte;
import org.web3j.abi.datatypes.primitive.Char;
import org.web3j.abi.datatypes.primitive.Double;
import org.web3j.abi.datatypes.primitive.Float;
import org.web3j.abi.datatypes.primitive.Long;
import org.web3j.abi.datatypes.primitive.Short;

/**
 * リクエスト時に受け取った標準のデータクラスを web3j が利用できるデータクラスに変換する処理を扱う。
 *
 * @param <ORIGINAL_TYPE> 変換後の web3j の Type インタフェースの内部データ型
 * @param <FROM_OBJECT> 標準データクラス
 */
@SuppressWarnings({ "rawtypes", "unchecked" })
public abstract class AbiTypeConverter<ORIGINAL_TYPE, FROM_OBJECT>
        implements ToAbiType<Type<ORIGINAL_TYPE>, FROM_OBJECT> {

    private static final Map<String, AbiTypeConverter<?, Object>> CONVERTER_MAPPING = Collections
            .unmodifiableMap(initConverterMapping());

    private final Class<Type<ORIGINAL_TYPE>> typeClass;

    protected AbiTypeConverter(Class typeClass) {
        this.typeClass = (Class<Type<ORIGINAL_TYPE>>) typeClass;
    }

    public Class<Type<ORIGINAL_TYPE>> getTypeClass() {
        return this.typeClass;
    }

    public static AbiTypeConverter<?, Object> getConverter(String abiType) {
        AbiTypeConverter<?, Object> converter = CONVERTER_MAPPING.get(abiType);
        if (converter == null) {
            throw new UnsupportedOperationException("Abi type '" + abiType + "' is not supported.");
        }

        return converter;
    }

    /**
     * 動的配列への変換処理を生成する。
     *
     * @param <ORG_TYPE> 変換後の web3j の Type インタフェースの内部データ型
     * @param <FROM_OBJECT> 標準データクラス
     * @param baseConverter 配列の各要素の変換処理
     * @return 動的配列への変換処理
     */
    public static <ORG_TYPE, FROM_OBJECT> AbiTypeConverter<List<Type<ORG_TYPE>>, List<FROM_OBJECT>> dynamicArrayConverter(
            final ToAbiType<?, FROM_OBJECT> baseConverter) {

        return new AbiTypeConverter<>(DynamicArray.class) {

            @Override
            public boolean isDynamic() {
                return true;
            }

            @Override
            public Type<?> toAbiType(List<FROM_OBJECT> values) {
                List<Type<?>> convertedValues = values.stream().map((value) -> baseConverter.toAbiType(value))
                        .collect(Collectors.toList());
                return new DynamicArray(baseConverter.getTypeClass(), convertedValues);
            }
        };
    }

    /**
     * 静的配列への変換処理を生成する。
     *
     * @param <ORG_TYPE> 変換後の web3j の Type インタフェースの内部データ型
     * @param <FROM_OBJECT> 標準データクラス
     * @param baseConverter 配列の各要素の変換処理
     * @param size 配列サイズ
     * @return 静的配列への変換処理
     */
    public static <ORG_TYPE, FROM_OBJECT> AbiTypeConverter<List<Type<ORG_TYPE>>, List<FROM_OBJECT>> staticArrayConverter(
            final ToAbiType<?, FROM_OBJECT> baseConverter, final int size) {

        if ((size < 0) || (size > 32)) {
            throw new UnsupportedOperationException("static array size '" + size + "' is not supported.");
        }

        return new AbiTypeConverter<>(StaticArray.class) {

            @Override
            public boolean isDynamic() {
                return baseConverter.isDynamic();
            }

            @Override
            public Type<?> toAbiType(List<FROM_OBJECT> values) {
                List<Type<?>> convertedValues = values.stream().map((value) -> baseConverter.toAbiType(value))
                        .collect(Collectors.toList());
                return initStaticArray(baseConverter.getTypeClass(), size, convertedValues);
            }
        };
    }

    private static abstract class StaticAbiTypeConverter<ORIGINAL_TYPE, FROM_OBJECT>
            extends AbiTypeConverter<ORIGINAL_TYPE, FROM_OBJECT> {

        protected StaticAbiTypeConverter(Class typeClass) {
            super(typeClass);
        }

        @Override
        public boolean isDynamic() {
            return false;
        }
    }

    private static Map<String, AbiTypeConverter<?, Object>> initConverterMapping() {
        AbiTypeConverter<Boolean, Object> boolConverter = new StaticAbiTypeConverter<>(Bool.class) {
            @Override
            public Type<?> toAbiType(Object value) {
                if (value instanceof Boolean boolValue) {
                    return new Bool(boolValue.booleanValue());
                }

                if(value instanceof String stringValue) {
                    if(stringValue.equals("true") || stringValue.equals("false")) {
                        return new Bool(Boolean.valueOf(stringValue));
                    }
                }

                throw new IllegalArgumentException("Input value '" + value + "' is not boolean value.");
            }
        };

        Map<String, AbiTypeConverter<?, Object>> mapping = new HashMap<>();
        mapping.put("address", new StaticAbiTypeConverter<String, Object>(Address.class) {
            @Override
            public Type<?> toAbiType(Object value) {
                return new Address(value.toString());
            }
        });
        mapping.put("bool", boolConverter);
        mapping.put("boolean", boolConverter);
        mapping.put("string", new AbiTypeConverter<String, Object>(Utf8String.class) {

            @Override
            public boolean isDynamic() {
                return true;
            }

            public Type<?> toAbiType(Object value) {
                return new Utf8String(value.toString());
            }
        });

        mapping.put("bytes", new AbiTypeConverter<byte[], Object>(DynamicBytes.class) {

            @Override
            public boolean isDynamic() {
                return true;
            }

            public Type<?> toAbiType(Object value) {
                return new DynamicBytes(toByteArray(value));
            }
        });
        mapping.put("byte", new StaticAbiTypeConverter<java.lang.Byte, Object>(Byte.class) {
            public Type<?> toAbiType(Object value) {
                if (value instanceof java.lang.Byte val) {
                    return new Byte(val.byteValue());
                }

                byte[] byteArray = toByteArray(value);
                if (byteArray.length != 1) {
                    throw new IllegalArgumentException("Input value '" + value + "' is not byte value.");
                }

                return new Byte(byteArray[0]);
            }
        });
        mapping.put("char", new StaticAbiTypeConverter<Character, Object>(Char.class) {
            public Type<?> toAbiType(Object value) {
                if (value instanceof Character val) {
                    return new Char(val);
                }
                String val = value.toString();
                if (val.length() != 1) {
                    throw new IllegalArgumentException("Input value '" + value + "' is not char value.");
                }
                return new Char(val.charAt(0));
            }
        });
        mapping.put("double", new StaticAbiTypeConverter<java.lang.Double, Object>(Double.class) {
            public Type<?> toAbiType(Object value) {
                if (value instanceof java.lang.Double val) {
                    return new Double(val);
                }
                if (value instanceof BigDecimal val) {
                    return new Double(val.doubleValue());
                }

                try {
                    return new Double(java.lang.Double.parseDouble(value.toString()));
                } catch (NumberFormatException exc) {
                    throw new IllegalArgumentException("Input value '" + value + "' is not double value.", exc);
                }
            }
        });
        mapping.put("float", new StaticAbiTypeConverter<java.lang.Float, Object>(Float.class) {
            public Type<?> toAbiType(Object value) {
                if (value instanceof java.lang.Float val) {
                    return new Float(val);
                }
                if (value instanceof BigDecimal val) {
                    return new Float(val.floatValue());
                }

                try {
                    return new Float(java.lang.Float.parseFloat(value.toString()));
                } catch (NumberFormatException exc) {
                    throw new IllegalArgumentException("Input value '" + value + "' is not float value.", exc);
                }
            }
        });
        mapping.put("uint", new StaticAbiTypeConverter<BigInteger, Object>(Uint.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint(toBigInteger(value));
            }
        });
        mapping.put("int", new StaticAbiTypeConverter<BigInteger, Object>(Int.class) {
            public Type<?> toAbiType(Object value) {
                return new Int(toBigInteger(value));
            }
        });
        mapping.put("long", new StaticAbiTypeConverter<java.lang.Long, Object>(Long.class) {
            public Type<?> toAbiType(Object value) {
                if (value instanceof java.lang.Long val) {
                    return new Long(val);
                }
                return new Long(toBigInteger(value).longValue());
            }
        });
        mapping.put("short", new StaticAbiTypeConverter<java.lang.Short, Object>(Short.class) {
            public Type<?> toAbiType(Object value) {
                if (value instanceof java.lang.Short val) {
                    return new Short(val);
                }
                return new Short(toBigInteger(value).shortValue());
            }
        });
        mapping.put("uint8", new StaticAbiTypeConverter<BigInteger, Object>(Uint8.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint8(toBigInteger(value));
            }
        });
        mapping.put("int8", new StaticAbiTypeConverter<BigInteger, Object>(Int8.class) {
            public Type<?> toAbiType(Object value) {
                return new Int8(toBigInteger(value));
            }
        });
        mapping.put("uint16", new StaticAbiTypeConverter<BigInteger, Object>(Uint16.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint16(toBigInteger(value));
            }
        });
        mapping.put("int16", new StaticAbiTypeConverter<BigInteger, Object>(Int16.class) {
            public Type<?> toAbiType(Object value) {
                return new Int16(toBigInteger(value));
            }
        });
        mapping.put("uint24", new StaticAbiTypeConverter<BigInteger, Object>(Uint24.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint24(toBigInteger(value));
            }
        });
        mapping.put("int24", new StaticAbiTypeConverter<BigInteger, Object>(Int24.class) {
            public Type<?> toAbiType(Object value) {
                return new Int24(toBigInteger(value));
            }
        });
        mapping.put("uint32", new StaticAbiTypeConverter<BigInteger, Object>(Uint32.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint32(toBigInteger(value));
            }
        });
        mapping.put("int32", new StaticAbiTypeConverter<BigInteger, Object>(Int32.class) {
            public Type<?> toAbiType(Object value) {
                return new Int32(toBigInteger(value));
            }
        });
        mapping.put("uint40", new StaticAbiTypeConverter<BigInteger, Object>(Uint40.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint40(toBigInteger(value));
            }
        });
        mapping.put("int40", new StaticAbiTypeConverter<BigInteger, Object>(Int40.class) {
            public Type<?> toAbiType(Object value) {
                return new Int40(toBigInteger(value));
            }
        });
        mapping.put("uint48", new StaticAbiTypeConverter<BigInteger, Object>(Uint48.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint48(toBigInteger(value));
            }
        });
        mapping.put("int48", new StaticAbiTypeConverter<BigInteger, Object>(Int48.class) {
            public Type<?> toAbiType(Object value) {
                return new Int48(toBigInteger(value));
            }
        });
        mapping.put("uint56", new StaticAbiTypeConverter<BigInteger, Object>(Uint56.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint56(toBigInteger(value));
            }
        });
        mapping.put("int56", new StaticAbiTypeConverter<BigInteger, Object>(Int56.class) {
            public Type<?> toAbiType(Object value) {
                return new Int56(toBigInteger(value));
            }
        });
        mapping.put("uint64", new StaticAbiTypeConverter<BigInteger, Object>(Uint64.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint64(toBigInteger(value));
            }
        });
        mapping.put("int64", new StaticAbiTypeConverter<BigInteger, Object>(Int64.class) {
            public Type<?> toAbiType(Object value) {
                return new Int64(toBigInteger(value));
            }
        });
        mapping.put("uint72", new StaticAbiTypeConverter<BigInteger, Object>(Uint72.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint72(toBigInteger(value));
            }
        });
        mapping.put("int72", new StaticAbiTypeConverter<BigInteger, Object>(Int72.class) {
            public Type<?> toAbiType(Object value) {
                return new Int72(toBigInteger(value));
            }
        });
        mapping.put("uint80", new StaticAbiTypeConverter<BigInteger, Object>(Uint80.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint80(toBigInteger(value));
            }
        });
        mapping.put("int80", new StaticAbiTypeConverter<BigInteger, Object>(Int80.class) {
            public Type<?> toAbiType(Object value) {
                return new Int80(toBigInteger(value));
            }
        });
        mapping.put("uint88", new StaticAbiTypeConverter<BigInteger, Object>(Uint88.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint88(toBigInteger(value));
            }
        });
        mapping.put("int88", new StaticAbiTypeConverter<BigInteger, Object>(Int88.class) {
            public Type<?> toAbiType(Object value) {
                return new Int88(toBigInteger(value));
            }
        });
        mapping.put("uint96", new StaticAbiTypeConverter<BigInteger, Object>(Uint96.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint96(toBigInteger(value));
            }
        });
        mapping.put("int96", new StaticAbiTypeConverter<BigInteger, Object>(Int96.class) {
            public Type<?> toAbiType(Object value) {
                return new Int96(toBigInteger(value));
            }
        });
        mapping.put("uint104", new StaticAbiTypeConverter<BigInteger, Object>(Uint104.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint104(toBigInteger(value));
            }
        });
        mapping.put("int104", new StaticAbiTypeConverter<BigInteger, Object>(Int104.class) {
            public Type<?> toAbiType(Object value) {
                return new Int104(toBigInteger(value));
            }
        });
        mapping.put("uint112", new StaticAbiTypeConverter<BigInteger, Object>(Uint112.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint112(toBigInteger(value));
            }
        });
        mapping.put("int112", new StaticAbiTypeConverter<BigInteger, Object>(Int112.class) {
            public Type<?> toAbiType(Object value) {
                return new Int112(toBigInteger(value));
            }
        });
        mapping.put("uint120", new StaticAbiTypeConverter<BigInteger, Object>(Uint120.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint120(toBigInteger(value));
            }
        });
        mapping.put("int120", new StaticAbiTypeConverter<BigInteger, Object>(Int120.class) {
            public Type<?> toAbiType(Object value) {
                return new Int120(toBigInteger(value));
            }
        });
        mapping.put("uint128", new StaticAbiTypeConverter<BigInteger, Object>(Uint128.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint128(toBigInteger(value));
            }
        });
        mapping.put("int128", new StaticAbiTypeConverter<BigInteger, Object>(Int128.class) {
            public Type<?> toAbiType(Object value) {
                return new Int128(toBigInteger(value));
            }
        });
        mapping.put("uint136", new StaticAbiTypeConverter<BigInteger, Object>(Uint136.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint136(toBigInteger(value));
            }
        });
        mapping.put("int136", new StaticAbiTypeConverter<BigInteger, Object>(Int136.class) {
            public Type<?> toAbiType(Object value) {
                return new Int136(toBigInteger(value));
            }
        });
        mapping.put("uint144", new StaticAbiTypeConverter<BigInteger, Object>(Uint144.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint144(toBigInteger(value));
            }
        });
        mapping.put("int144", new StaticAbiTypeConverter<BigInteger, Object>(Int144.class) {
            public Type<?> toAbiType(Object value) {
                return new Int144(toBigInteger(value));
            }
        });
        mapping.put("uint152", new StaticAbiTypeConverter<BigInteger, Object>(Uint152.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint152(toBigInteger(value));
            }
        });
        mapping.put("int152", new StaticAbiTypeConverter<BigInteger, Object>(Int152.class) {
            public Type<?> toAbiType(Object value) {
                return new Int152(toBigInteger(value));
            }
        });
        mapping.put("uint160", new StaticAbiTypeConverter<BigInteger, Object>(Uint160.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint160(toBigInteger(value));
            }
        });
        mapping.put("int160", new StaticAbiTypeConverter<BigInteger, Object>(Int160.class) {
            public Type<?> toAbiType(Object value) {
                return new Int160(toBigInteger(value));
            }
        });
        mapping.put("uint168", new StaticAbiTypeConverter<BigInteger, Object>(Uint168.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint168(toBigInteger(value));
            }
        });
        mapping.put("int168", new StaticAbiTypeConverter<BigInteger, Object>(Int168.class) {
            public Type<?> toAbiType(Object value) {
                return new Int168(toBigInteger(value));
            }
        });
        mapping.put("uint176", new StaticAbiTypeConverter<BigInteger, Object>(Uint176.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint176(toBigInteger(value));
            }
        });
        mapping.put("int176", new StaticAbiTypeConverter<BigInteger, Object>(Int176.class) {
            public Type<?> toAbiType(Object value) {
                return new Int176(toBigInteger(value));
            }
        });
        mapping.put("uint184", new StaticAbiTypeConverter<BigInteger, Object>(Uint184.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint184(toBigInteger(value));
            }
        });
        mapping.put("int184", new StaticAbiTypeConverter<BigInteger, Object>(Int184.class) {
            public Type<?> toAbiType(Object value) {
                return new Int184(toBigInteger(value));
            }
        });
        mapping.put("uint192", new StaticAbiTypeConverter<BigInteger, Object>(Uint192.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint192(toBigInteger(value));
            }
        });
        mapping.put("int192", new StaticAbiTypeConverter<BigInteger, Object>(Int192.class) {
            public Type<?> toAbiType(Object value) {
                return new Int192(toBigInteger(value));
            }
        });
        mapping.put("uint200", new StaticAbiTypeConverter<BigInteger, Object>(Uint200.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint200(toBigInteger(value));
            }
        });
        mapping.put("int200", new StaticAbiTypeConverter<BigInteger, Object>(Int200.class) {
            public Type<?> toAbiType(Object value) {
                return new Int200(toBigInteger(value));
            }
        });
        mapping.put("uint208", new StaticAbiTypeConverter<BigInteger, Object>(Uint208.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint208(toBigInteger(value));
            }
        });
        mapping.put("int208", new StaticAbiTypeConverter<BigInteger, Object>(Int208.class) {
            public Type<?> toAbiType(Object value) {
                return new Int208(toBigInteger(value));
            }
        });
        mapping.put("uint216", new StaticAbiTypeConverter<BigInteger, Object>(Uint216.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint216(toBigInteger(value));
            }
        });
        mapping.put("int216", new StaticAbiTypeConverter<BigInteger, Object>(Int216.class) {
            public Type<?> toAbiType(Object value) {
                return new Int216(toBigInteger(value));
            }
        });
        mapping.put("uint224", new StaticAbiTypeConverter<BigInteger, Object>(Uint224.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint224(toBigInteger(value));
            }
        });
        mapping.put("int224", new StaticAbiTypeConverter<BigInteger, Object>(Int224.class) {
            public Type<?> toAbiType(Object value) {
                return new Int224(toBigInteger(value));
            }
        });
        mapping.put("uint232", new StaticAbiTypeConverter<BigInteger, Object>(Uint232.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint232(toBigInteger(value));
            }
        });
        mapping.put("int232", new StaticAbiTypeConverter<BigInteger, Object>(Int232.class) {
            public Type<?> toAbiType(Object value) {
                return new Int232(toBigInteger(value));
            }
        });
        mapping.put("uint240", new StaticAbiTypeConverter<BigInteger, Object>(Uint240.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint240(toBigInteger(value));
            }
        });
        mapping.put("int240", new StaticAbiTypeConverter<BigInteger, Object>(Int240.class) {
            public Type<?> toAbiType(Object value) {
                return new Int240(toBigInteger(value));
            }
        });
        mapping.put("uint248", new StaticAbiTypeConverter<BigInteger, Object>(Uint248.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint248(toBigInteger(value));
            }
        });
        mapping.put("int248", new StaticAbiTypeConverter<BigInteger, Object>(Int248.class) {
            public Type<?> toAbiType(Object value) {
                return new Int248(toBigInteger(value));
            }
        });
        mapping.put("uint256", new StaticAbiTypeConverter<BigInteger, Object>(Uint256.class) {
            public Type<?> toAbiType(Object value) {
                return new Uint256(toBigInteger(value));
            }
        });
        mapping.put("int256", new StaticAbiTypeConverter<BigInteger, Object>(Int256.class) {
            public Type<?> toAbiType(Object value) {
                return new Int256(toBigInteger(value));
            }
        });

        mapping.put("bytes1", new StaticAbiTypeConverter<byte[], Object>(Bytes1.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes1(toByteArray(value));
            }
        });
        mapping.put("bytes2", new StaticAbiTypeConverter<byte[], Object>(Bytes2.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes2(toByteArray(value));
            }
        });
        mapping.put("bytes3", new StaticAbiTypeConverter<byte[], Object>(Bytes3.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes3(toByteArray(value));
            }
        });
        mapping.put("bytes4", new StaticAbiTypeConverter<byte[], Object>(Bytes4.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes4(toByteArray(value));
            }
        });
        mapping.put("bytes5", new StaticAbiTypeConverter<byte[], Object>(Bytes5.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes5(toByteArray(value));
            }
        });
        mapping.put("bytes6", new StaticAbiTypeConverter<byte[], Object>(Bytes6.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes6(toByteArray(value));
            }
        });
        mapping.put("bytes7", new StaticAbiTypeConverter<byte[], Object>(Bytes7.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes7(toByteArray(value));
            }
        });
        mapping.put("bytes8", new StaticAbiTypeConverter<byte[], Object>(Bytes8.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes8(toByteArray(value));
            }
        });
        mapping.put("bytes9", new StaticAbiTypeConverter<byte[], Object>(Bytes9.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes9(toByteArray(value));
            }
        });
        mapping.put("bytes10", new StaticAbiTypeConverter<byte[], Object>(Bytes10.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes10(toByteArray(value));
            }
        });
        mapping.put("bytes11", new StaticAbiTypeConverter<byte[], Object>(Bytes11.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes11(toByteArray(value));
            }
        });
        mapping.put("bytes12", new StaticAbiTypeConverter<byte[], Object>(Bytes12.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes12(toByteArray(value));
            }
        });
        mapping.put("bytes13", new StaticAbiTypeConverter<byte[], Object>(Bytes13.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes13(toByteArray(value));
            }
        });
        mapping.put("bytes14", new StaticAbiTypeConverter<byte[], Object>(Bytes14.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes14(toByteArray(value));
            }
        });
        mapping.put("bytes15", new StaticAbiTypeConverter<byte[], Object>(Bytes15.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes15(toByteArray(value));
            }
        });
        mapping.put("bytes16", new StaticAbiTypeConverter<byte[], Object>(Bytes16.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes16(toByteArray(value));
            }
        });
        mapping.put("bytes17", new StaticAbiTypeConverter<byte[], Object>(Bytes17.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes17(toByteArray(value));
            }
        });
        mapping.put("bytes18", new StaticAbiTypeConverter<byte[], Object>(Bytes18.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes18(toByteArray(value));
            }
        });
        mapping.put("bytes19", new StaticAbiTypeConverter<byte[], Object>(Bytes19.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes19(toByteArray(value));
            }
        });
        mapping.put("bytes20", new StaticAbiTypeConverter<byte[], Object>(Bytes20.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes20(toByteArray(value));
            }
        });
        mapping.put("bytes21", new StaticAbiTypeConverter<byte[], Object>(Bytes21.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes21(toByteArray(value));
            }
        });
        mapping.put("bytes22", new StaticAbiTypeConverter<byte[], Object>(Bytes22.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes22(toByteArray(value));
            }
        });
        mapping.put("bytes23", new StaticAbiTypeConverter<byte[], Object>(Bytes23.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes23(toByteArray(value));
            }
        });
        mapping.put("bytes24", new StaticAbiTypeConverter<byte[], Object>(Bytes24.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes24(toByteArray(value));
            }
        });
        mapping.put("bytes25", new StaticAbiTypeConverter<byte[], Object>(Bytes25.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes25(toByteArray(value));
            }
        });
        mapping.put("bytes26", new StaticAbiTypeConverter<byte[], Object>(Bytes26.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes26(toByteArray(value));
            }
        });
        mapping.put("bytes27", new StaticAbiTypeConverter<byte[], Object>(Bytes27.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes27(toByteArray(value));
            }
        });
        mapping.put("bytes28", new StaticAbiTypeConverter<byte[], Object>(Bytes28.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes28(toByteArray(value));
            }
        });
        mapping.put("bytes29", new StaticAbiTypeConverter<byte[], Object>(Bytes29.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes29(toByteArray(value));
            }
        });
        mapping.put("bytes30", new StaticAbiTypeConverter<byte[], Object>(Bytes30.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes30(toByteArray(value));
            }
        });
        mapping.put("bytes31", new StaticAbiTypeConverter<byte[], Object>(Bytes31.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes31(toByteArray(value));
            }
        });
        mapping.put("bytes32", new StaticAbiTypeConverter<byte[], Object>(Bytes32.class) {
            public Type<?> toAbiType(Object value) {
                return new Bytes32(toByteArray(value));
            }
        });

        return mapping;
    }

    private static BigInteger toBigInteger(Object value) {
        if (value instanceof java.lang.Integer val) {
            return BigInteger.valueOf(val.longValue());
        }
        if (value instanceof java.lang.Long val) {
            return BigInteger.valueOf(val.longValue());
        }
        if (value instanceof String val) {
            return new BigInteger(val);
        }
        if (value instanceof BigDecimal val) {
            return val.toBigInteger();
        }
        if (value instanceof BigInteger val) {
            return val;
        }

        try {
            return new BigInteger(value.toString());
        } catch (NumberFormatException exc) {
            throw new IllegalArgumentException("Input value '" + value + "' is not numeric value.", exc);
        }
    }

    private static byte[] toByteArray(Object value) {
        if (value instanceof byte[] val) {
            return val;
        }

        String val = value.toString();
        if (val.startsWith("0x")) {
            return DatatypeConverter.parseHexBinary(val.substring(2));
        }

        return Arrays.copyOf(val.getBytes(StandardCharsets.US_ASCII), 32);
    }

    private static StaticArray initStaticArray(Class clazz, int size, List values) {
        return switch (size) {
            case 0 -> new StaticArray0(clazz, values);
            case 1 -> new StaticArray1(clazz, values);
            case 2 -> new StaticArray2(clazz, values);
            case 3 -> new StaticArray3(clazz, values);
            case 4 -> new StaticArray4(clazz, values);
            case 5 -> new StaticArray5(clazz, values);
            case 6 -> new StaticArray6(clazz, values);
            case 7 -> new StaticArray7(clazz, values);
            case 8 -> new StaticArray8(clazz, values);
            case 9 -> new StaticArray9(clazz, values);
            case 10 -> new StaticArray10(clazz, values);
            case 11 -> new StaticArray11(clazz, values);
            case 12 -> new StaticArray12(clazz, values);
            case 13 -> new StaticArray13(clazz, values);
            case 14 -> new StaticArray14(clazz, values);
            case 15 -> new StaticArray15(clazz, values);
            case 16 -> new StaticArray16(clazz, values);
            case 17 -> new StaticArray17(clazz, values);
            case 18 -> new StaticArray18(clazz, values);
            case 19 -> new StaticArray19(clazz, values);
            case 20 -> new StaticArray20(clazz, values);
            case 21 -> new StaticArray21(clazz, values);
            case 22 -> new StaticArray22(clazz, values);
            case 23 -> new StaticArray23(clazz, values);
            case 24 -> new StaticArray24(clazz, values);
            case 25 -> new StaticArray25(clazz, values);
            case 26 -> new StaticArray26(clazz, values);
            case 27 -> new StaticArray27(clazz, values);
            case 28 -> new StaticArray28(clazz, values);
            case 29 -> new StaticArray29(clazz, values);
            case 30 -> new StaticArray30(clazz, values);
            case 31 -> new StaticArray31(clazz, values);
            case 32 -> new StaticArray32(clazz, values);
            default -> throw new IllegalArgumentException("Unexpected static array size: " + size);
        };
    }
}

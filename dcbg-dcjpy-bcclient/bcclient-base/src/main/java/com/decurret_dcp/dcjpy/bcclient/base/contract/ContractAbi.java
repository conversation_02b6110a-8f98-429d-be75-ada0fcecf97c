package com.decurret_dcp.dcjpy.bcclient.base.contract;

import com.decurret_dcp.dcjpy.bcclient.base.exception.ContractNotFoundException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ContractAbi {

    private final String name;

    private final String address;

    private final Map<String, ContractFunction> functionMap;

    public ContractAbi(String name, String address, List<ContractFunction> functionInfoList) {
        this.name = name;
        this.address = address;
        this.functionMap = functionInfoList.stream()
                .collect(Collectors.toMap(function -> function.getName(), function -> function));
    }

    public String getName() {
        return name;
    }

    public String getAddress() {
        return address;
    }

    public ContractFunction[] getFunctionInfos() {
        return this.functionMap.values().toArray(new ContractFunction[0]);
    }

    public boolean hasFunction(String name) {
        return this.functionMap.contains<PERSON>ey(name);
    }

    public ContractFunction getFunction(String name) throws ContractNotFoundException {
        ContractFunction function = this.functionMap.get(name);
        if (function == null) {
            throw new ContractNotFoundException("contractMethod not found : " + name);
        }

        return function;
    }
}

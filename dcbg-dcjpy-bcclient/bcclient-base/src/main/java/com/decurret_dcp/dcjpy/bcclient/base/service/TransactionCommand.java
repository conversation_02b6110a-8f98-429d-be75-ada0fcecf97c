package com.decurret_dcp.dcjpy.bcclient.base.service;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

import com.decurret_dcp.dcjpy.bcclient.base.value.TransactionValue;
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId;

@Data
@Builder
public class TransactionCommand {

    public final String requestId;

    public final ZoneId zoneId;

    public final String contractName;

    public final String method;

    public final Map<String, Object> args;

    public final String traceId;

    public static TransactionCommand create(TransactionValue transaction) {
        return TransactionCommand.builder()
                .requestId(transaction.requestId)
                .zoneId(transaction.zoneId)
                .contractName(transaction.contractName)
                .method(transaction.method)
                .args(transaction.args)
                .build();
    }
}

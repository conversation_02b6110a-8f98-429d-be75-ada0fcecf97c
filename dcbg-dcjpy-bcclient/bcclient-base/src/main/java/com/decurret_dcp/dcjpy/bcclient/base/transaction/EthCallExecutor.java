package com.decurret_dcp.dcjpy.bcclient.base.transaction;

import com.decurret_dcp.dcjpy.bcclient.base.credential.Web3jCredentialHolder;
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractFunction;
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractOutputParameter;
import com.decurret_dcp.dcjpy.bcclient.base.exception.BlockchainIOException;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.WebSocketConnectionPoolBase;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.binary.Hex;
import org.springframework.lang.NonNull;
import org.web3j.crypto.Credentials;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class EthCallExecutor {

    private final Web3jCredentialHolder credentialHolder;

    public EthCallExecutor(Web3jCredentialHolder credentialHolder) {
        this.credentialHolder = credentialHolder;
    }

    public Map<String, Object> callTransaction(
            WebSocketConnectionPoolBase connectionPool, EthTransactionCommand command
    ) {
        String encodedFunction = command.encode();
        Credentials credentials = this.credentialHolder.getCredentials();

        Transaction transaction = Transaction.createEthCallTransaction(
                credentials.getAddress(), command.contractAddress(), encodedFunction
        );

        Web3j web3j = connectionPool.getWebSocketConnection();
        EthCall ethCall;
        try {
            ethCall = web3j.ethCall(transaction, DefaultBlockParameterName.LATEST).send();
        } catch (IOException ioExc) {
            log.error("Call request failed. contractName : {}, functionName : {}",
                      command.contractAbi.getName(), command.contractFunction.getName(), ioExc);
            throw new BlockchainIOException("failed to ethCall", ioExc);
        }

        ContractFunction contractFunction = command.contractFunction;
        return this.decodeCallResult(ethCall.getValue(), contractFunction);
    }

    Map<String, Object> decodeCallResult(@NonNull String value, @NonNull ContractFunction contractFunction) {
        ContractOutputParameter<Map<String, Object>> outputParam = contractFunction.getOutputParameter();
        // value は '0x' 始まりなので、はじめの2文字を除いてからデコードする
        Map<String, Object> decodedValue = outputParam.decodeToNative(value.substring(2));

        return toStringMap(decodedValue);
    }

    private static Map<String, Object> toStringMap(Map<?, ?> decodedValue) {
        return decodedValue.entrySet().stream().collect(Collectors.toMap(
                entry -> entry.getKey().toString(),
                entry -> toStringValue(entry.getValue())
        ));
    }

    private static Object toStringValue(Object value) {
        if (value instanceof byte[] byteValues) {
            return "0x" + new String(Hex.encodeHex(byteValues));
        }
        if (value instanceof Boolean boolValue) {
            return boolValue;
        }
        if (value instanceof List<?> listValue) {
            return listValue.stream().map(innerValue -> toStringValue(innerValue)).collect(Collectors.toList());
        }
        if (value instanceof Map<?, ?> mapValue) {
            return toStringMap(mapValue);
        }

        return value.toString();
    }
}

package com.decurret_dcp.dcjpy.bcclient.base.value;

public enum TransactionStatus {

    /** 依頼受付中。 */
    QUEUING("queuing"),

    /** 依頼受付失敗。 */
    QUEUING_FAILED("queuing_failed", true),

    /** DLT 送信前。 */
    BEFORE_SENT("before_sent"),

    /** DLT 送信済み。 */
    SENT("sent"),

    /** 確定済み。 */
    COMPLETED("completed", true),

    /** 処理失敗。 */
    REVERTED("reverted", true);

    private final String value;

    private final boolean isEnd;

    private TransactionStatus(String value) {
        this(value, false);
    }

    private TransactionStatus(String value, boolean isEnd) {
        this.value = value;
        this.isEnd = isEnd;
    }

    public static TransactionStatus of(String status) {
        for (TransactionStatus target : TransactionStatus.values()) {
            if (target.getValue().equalsIgnoreCase(status)) {
                return target;
            }
        }

        throw new IllegalArgumentException("Invalid transaction_status : " + status);
    }

    public String getValue() {
        return this.value;
    }

    public boolean isEnd() {
        return this.isEnd;
    }
}

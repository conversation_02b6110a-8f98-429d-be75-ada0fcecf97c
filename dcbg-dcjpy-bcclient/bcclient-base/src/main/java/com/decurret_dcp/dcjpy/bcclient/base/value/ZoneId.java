package com.decurret_dcp.dcjpy.bcclient.base.value;

import java.util.concurrent.ConcurrentHashMap;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class ZoneId {

    private static final ConcurrentHashMap<String, ZoneId> CACHE = new ConcurrentHashMap<>();

    private final String value;

    private ZoneId(String value) {
        this.value = value;
    }

    public static ZoneId of(String zoneId) {
        return CACHE.computeIfAbsent(zoneId, value -> new ZoneId(value));
    }

    public String getValue() {
        return this.value;
    }
}

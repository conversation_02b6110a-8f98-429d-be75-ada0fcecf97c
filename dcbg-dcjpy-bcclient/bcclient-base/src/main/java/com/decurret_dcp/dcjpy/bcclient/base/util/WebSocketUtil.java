package com.decurret_dcp.dcjpy.bcclient.base.util;

import org.springframework.lang.NonNull;
import org.web3j.protocol.websocket.WebSocketClient;
import org.web3j.protocol.websocket.WebSocketService;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;

/**
 * WebSocket関連の汎用クラス
 */
public class WebSocketUtil {

    private static final String WEB_SOCKET_URI_PREFIX = "ws://";
    private static final String WEB_SOCKET_SECURE_URI_PREFIX = "wss://";
    private static final String COLON = ":";

    /**
     * 指定したホストとポートからWebSocketServiceを生成する
     *
     * @param host                ホスト
     * @param port                ポート
     * @param useSecureConnection 暗号化通信を使用する
     * @return WebSocketService
     */
    @NonNull
    public static WebSocketService generateWebSocketService(
            @NonNull String host, @NonNull String port, @NonNull boolean useSecureConnection
    ) {
        URI uri = generateWebSocketUri(host, port, useSecureConnection);
        return generateWebSocketService(uri);
    }

    /**
     * WebSocket用のURIを生成する
     *
     * @param host                ホスト
     * @param port                ポート
     * @param useSecureConnection 暗号化通信を使用する
     * @return URI WebSocketUri
     */
    @NonNull
    static URI generateWebSocketUri(@NonNull String host, @NonNull String port, @NonNull boolean useSecureConnection) {
        StringBuilder sb = new StringBuilder();
        sb.append(useSecureConnection ? WEB_SOCKET_SECURE_URI_PREFIX : WEB_SOCKET_URI_PREFIX);
        sb.append(host);
        sb.append(COLON);
        sb.append(port);

        try {
            return new URI(sb.toString());
        } catch (URISyntaxException uriExc) {
            throw new RuntimeException("Invalid websocket url", uriExc);
        }
    }

    /**
     * 指定したURIからWebSocketServiceを生成する
     *
     * @param uri WebSocket URI
     * @return WebSocketService
     */
    @NonNull
    static WebSocketService generateWebSocketService(@NonNull URI uri) {
        WebSocketService webSocketService = new WebSocketService(new WebSocketClient(uri), false);
        try {
            webSocketService.connect();
        } catch (IOException ioExc) {
            webSocketService.close();

            throw new RuntimeException("Websocket connection failed", ioExc);
        }

        return webSocketService;
    }
}

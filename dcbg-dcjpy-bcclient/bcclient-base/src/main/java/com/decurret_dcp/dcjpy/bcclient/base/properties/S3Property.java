package com.decurret_dcp.dcjpy.bcclient.base.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;
import org.springframework.util.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.ToString;

@ConstructorBinding
@ConfigurationProperties(prefix = "s3")
@AllArgsConstructor
@ToString
@Builder
public class S3Property {

    public final String contractBucketName;

    public final String externalContractBucketName;

    public final String localEndpoint;

    public final String localEndpointPort;

    /**
     * AWS 環境の S3 に接続するかどうか。
     *
     * @return AWS 環境の S3 に接続する場合 true
     */
    public boolean toAwsConnection() {
        return (StringUtils.hasText(this.localEndpoint) == false);
    }
}

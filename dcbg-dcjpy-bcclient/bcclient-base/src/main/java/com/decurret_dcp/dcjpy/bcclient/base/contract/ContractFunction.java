package com.decurret_dcp.dcjpy.bcclient.base.contract;

import lombok.Value;

import java.util.List;
import java.util.Map;

@Value
public class ContractFunction {

    private String name;

    private List<ContractInputParameter> inputParameterList;

    private ContractOutputParameter<Map<String, Object>> outputParameter;

    @SuppressWarnings("unchecked")
    public ContractFunction(
            String name, List<ContractInputParameter> inputParameterList,
            List<ContractOutputParameter<?>> outputParameterList
    ) {

        this.name = name;
        this.inputParameterList = List.copyOf(inputParameterList);
        this.outputParameter = (ContractOutputParameter<Map<String, Object>>) ContractOutputParameter.create(
                "outputs", "tuple", outputParameterList);
    }
}

package com.decurret_dcp.dcjpy.bcclient.base.adaptor;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bcclient.base.properties.S3Property;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;

/**
 * S3操作用の汎用クラス
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class AwsS3Adaptor {

    private final S3Property s3Property;

    S3Client initS3Client() {
        return this.initS3Client(false);
    }

    S3Client initS3Client(boolean isExternalAbiBucket) {
        S3ClientBuilder builder = S3Client.builder();
        if (this.s3Property.toAwsConnection() == false) {
            AwsCredentials localCredentials = AwsBasicCredentials.create("access123", "secret123");
            String endpoint = this.s3Property.localEndpoint + ":" + this.s3Property.localEndpointPort;

            builder = builder
                    .region(Region.AP_NORTHEAST_1)
                    .endpointOverride(URI.create(endpoint))
                    .credentialsProvider(StaticCredentialsProvider.create(localCredentials));

            log.error(
                    "Change the S3 connection url to {}. "
                            + "If this message is displayed in running on AWS environment, "
                            + "check whether the environment variable is correct.",
                    endpoint
            );
        }

        return builder.build();
    }

    /**
     * 指定したバケットのオブジェクト名およびバイト配列の一覧を取得する。
     *
     * @param bucketName バケット名
     * @param filterCondition オブジェクト名に対するフィルタ条件
     * @return オブジェクト名とバイト配列の紐付け
     */
    public Map<String, byte[]> fetchAbiContents(String bucketName, Predicate<String> filterCondition) {
        Map<String, byte[]> abiContents = new HashMap<>();

        try (S3Client s3Client = this.initS3Client()) {
            String continuationToken = null;
            do {
                ListObjectsV2Request request = ListObjectsV2Request.builder()
                        .bucket(bucketName)
                        .continuationToken(continuationToken)
                        .build();
                ListObjectsV2Response response = s3Client.listObjectsV2(request);

                Map<String, byte[]> result = response.contents().stream()
                        .map((obj) -> obj.key())
                        .filter(filterCondition)
                        .collect(Collectors.toMap(
                                (key) -> key,
                                (key) -> {
                                    GetObjectRequest objectRequest = GetObjectRequest.builder()
                                            .bucket(bucketName)
                                            .key(key)
                                            .build();
                                    return s3Client.getObjectAsBytes(objectRequest).asByteArray();
                                }
                        ));

                abiContents.putAll(result);
                continuationToken = response.nextContinuationToken();
            } while (continuationToken != null);

            return abiContents;
        }
    }

    /**
     * 指定したバケットのオブジェクトをバイト配列で取得する
     *
     * @param bucketName バケット名
     * @param key        オベジェクトキー
     * @return ファイル内容のbyte配列
     */
    @NonNull
    public byte[] fetchObjectAsByte(@NonNull String bucketName, @NonNull String key) {
        try (S3Client s3Client = this.initS3Client(true)) {
            GetObjectRequest objectRequest = GetObjectRequest
                    .builder()
                    .key(key)
                    .bucket(bucketName)
                    .build();
            return s3Client.getObjectAsBytes(objectRequest).asByteArray();
        }
    }
}

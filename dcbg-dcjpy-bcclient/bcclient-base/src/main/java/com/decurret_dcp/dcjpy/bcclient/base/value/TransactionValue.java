package com.decurret_dcp.dcjpy.bcclient.base.value;

import java.math.BigInteger;
import java.util.Map;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class TransactionValue {

    public final String requestId;

    public final TransactionStatus transactionStatus;

    public final ZoneId zoneId;

    public final String contractName;

    public final String method;

    public final Map<String, Object> args;

    public final String traceId;

    public final String nonceKey;

    public final BigInteger nonce;

    public final Long sentAt;

    public final String transactionHash;

    public final String revertReason;

    public final boolean inSending;
}

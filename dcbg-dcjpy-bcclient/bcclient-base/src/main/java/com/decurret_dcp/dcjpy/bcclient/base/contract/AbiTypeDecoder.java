package com.decurret_dcp.dcjpy.bcclient.base.contract;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Bool;
import org.web3j.abi.datatypes.DynamicBytes;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.Bytes1;
import org.web3j.abi.datatypes.generated.Bytes10;
import org.web3j.abi.datatypes.generated.Bytes11;
import org.web3j.abi.datatypes.generated.Bytes12;
import org.web3j.abi.datatypes.generated.Bytes13;
import org.web3j.abi.datatypes.generated.Bytes14;
import org.web3j.abi.datatypes.generated.Bytes15;
import org.web3j.abi.datatypes.generated.Bytes16;
import org.web3j.abi.datatypes.generated.Bytes17;
import org.web3j.abi.datatypes.generated.Bytes18;
import org.web3j.abi.datatypes.generated.Bytes19;
import org.web3j.abi.datatypes.generated.Bytes2;
import org.web3j.abi.datatypes.generated.Bytes20;
import org.web3j.abi.datatypes.generated.Bytes21;
import org.web3j.abi.datatypes.generated.Bytes22;
import org.web3j.abi.datatypes.generated.Bytes23;
import org.web3j.abi.datatypes.generated.Bytes24;
import org.web3j.abi.datatypes.generated.Bytes25;
import org.web3j.abi.datatypes.generated.Bytes26;
import org.web3j.abi.datatypes.generated.Bytes27;
import org.web3j.abi.datatypes.generated.Bytes28;
import org.web3j.abi.datatypes.generated.Bytes29;
import org.web3j.abi.datatypes.generated.Bytes3;
import org.web3j.abi.datatypes.generated.Bytes30;
import org.web3j.abi.datatypes.generated.Bytes31;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.abi.datatypes.generated.Bytes4;
import org.web3j.abi.datatypes.generated.Bytes5;
import org.web3j.abi.datatypes.generated.Bytes6;
import org.web3j.abi.datatypes.generated.Bytes7;
import org.web3j.abi.datatypes.generated.Bytes8;
import org.web3j.abi.datatypes.generated.Bytes9;
import org.web3j.abi.datatypes.generated.Int104;
import org.web3j.abi.datatypes.generated.Int112;
import org.web3j.abi.datatypes.generated.Int120;
import org.web3j.abi.datatypes.generated.Int128;
import org.web3j.abi.datatypes.generated.Int136;
import org.web3j.abi.datatypes.generated.Int144;
import org.web3j.abi.datatypes.generated.Int152;
import org.web3j.abi.datatypes.generated.Int16;
import org.web3j.abi.datatypes.generated.Int160;
import org.web3j.abi.datatypes.generated.Int168;
import org.web3j.abi.datatypes.generated.Int176;
import org.web3j.abi.datatypes.generated.Int184;
import org.web3j.abi.datatypes.generated.Int192;
import org.web3j.abi.datatypes.generated.Int200;
import org.web3j.abi.datatypes.generated.Int208;
import org.web3j.abi.datatypes.generated.Int216;
import org.web3j.abi.datatypes.generated.Int224;
import org.web3j.abi.datatypes.generated.Int232;
import org.web3j.abi.datatypes.generated.Int24;
import org.web3j.abi.datatypes.generated.Int240;
import org.web3j.abi.datatypes.generated.Int248;
import org.web3j.abi.datatypes.generated.Int256;
import org.web3j.abi.datatypes.generated.Int32;
import org.web3j.abi.datatypes.generated.Int40;
import org.web3j.abi.datatypes.generated.Int48;
import org.web3j.abi.datatypes.generated.Int56;
import org.web3j.abi.datatypes.generated.Int64;
import org.web3j.abi.datatypes.generated.Int72;
import org.web3j.abi.datatypes.generated.Int8;
import org.web3j.abi.datatypes.generated.Int80;
import org.web3j.abi.datatypes.generated.Int88;
import org.web3j.abi.datatypes.generated.Int96;
import org.web3j.abi.datatypes.generated.Uint104;
import org.web3j.abi.datatypes.generated.Uint112;
import org.web3j.abi.datatypes.generated.Uint120;
import org.web3j.abi.datatypes.generated.Uint128;
import org.web3j.abi.datatypes.generated.Uint136;
import org.web3j.abi.datatypes.generated.Uint144;
import org.web3j.abi.datatypes.generated.Uint152;
import org.web3j.abi.datatypes.generated.Uint16;
import org.web3j.abi.datatypes.generated.Uint160;
import org.web3j.abi.datatypes.generated.Uint168;
import org.web3j.abi.datatypes.generated.Uint176;
import org.web3j.abi.datatypes.generated.Uint184;
import org.web3j.abi.datatypes.generated.Uint192;
import org.web3j.abi.datatypes.generated.Uint200;
import org.web3j.abi.datatypes.generated.Uint208;
import org.web3j.abi.datatypes.generated.Uint216;
import org.web3j.abi.datatypes.generated.Uint224;
import org.web3j.abi.datatypes.generated.Uint232;
import org.web3j.abi.datatypes.generated.Uint24;
import org.web3j.abi.datatypes.generated.Uint240;
import org.web3j.abi.datatypes.generated.Uint248;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.abi.datatypes.generated.Uint32;
import org.web3j.abi.datatypes.generated.Uint40;
import org.web3j.abi.datatypes.generated.Uint48;
import org.web3j.abi.datatypes.generated.Uint56;
import org.web3j.abi.datatypes.generated.Uint64;
import org.web3j.abi.datatypes.generated.Uint72;
import org.web3j.abi.datatypes.generated.Uint8;
import org.web3j.abi.datatypes.generated.Uint80;
import org.web3j.abi.datatypes.generated.Uint88;
import org.web3j.abi.datatypes.generated.Uint96;
import org.web3j.utils.Numeric;

public abstract class AbiTypeDecoder<ABI_TYPE extends Type<ORIGINAL_TYPE>, ORIGINAL_TYPE> {

    private static final Map<String, AbiTypeDecoder<?, ?>> DECODER_MAPPING = Collections
            .unmodifiableMap(initDecoderMapping());

    static final int MAX_BYTE_LENGTH_FOR_HEX_STRING = Type.MAX_BYTE_LENGTH << 1;

    public abstract boolean isDynamicType();

    public int encodedByteLength() {
        if (this.isDynamicType()) {
            throw new UnsupportedOperationException("Dynamic value is not specified byte length.");
        }

        return Type.MAX_BYTE_LENGTH;
    }

    public abstract ORIGINAL_TYPE decodeToNative(String input);

    public abstract ABI_TYPE decode(String input);

    public static AbiTypeDecoder<?, ?> getDecoder(String abiType) {
        AbiTypeDecoder<?, ?> decoder = DECODER_MAPPING.get(abiType);
        if (decoder == null) {
            throw new UnsupportedOperationException("Abi type '" + abiType + "' is not supported.");
        }

        return decoder;
    }

    @SuppressWarnings("unchecked")
    private static UnsignedNumericDecoder<Uint256> getUint256Decoder() {
        return (UnsignedNumericDecoder<Uint256>) AbiTypeDecoder.getDecoder("uint256");
    }

    public static <ABI_TYPE extends Type<ORIGINAL_TYPE>, ORIGINAL_TYPE> FixedSizeArrayDecoder<ABI_TYPE, ORIGINAL_TYPE> fixedSizeArrayDecoder(
            int arraySize, AbiTypeDecoder<ABI_TYPE, ORIGINAL_TYPE> baseDecoder) {

        return new FixedSizeArrayDecoder<>(arraySize, baseDecoder);
    }

    public static <ABI_TYPE extends Type<ORIGINAL_TYPE>, ORIGINAL_TYPE> DynamicArrayDecoder<ABI_TYPE, ORIGINAL_TYPE> dynamicArrayDecoder(
            AbiTypeDecoder<ABI_TYPE, ORIGINAL_TYPE> baseDecoder) {

        return new DynamicArrayDecoder<>(baseDecoder);
    }

    private static class FixedSizeArrayDecoder<ABI_TYPE extends Type<ORIGINAL_TYPE>, ORIGINAL_TYPE>
            extends AbiTypeDecoder<Type<List<ORIGINAL_TYPE>>, List<ORIGINAL_TYPE>> {

        private final int arraySize;
        private final AbiTypeDecoder<ABI_TYPE, ORIGINAL_TYPE> baseDecoder;

        private FixedSizeArrayDecoder(int arraySize, AbiTypeDecoder<ABI_TYPE, ORIGINAL_TYPE> baseDecoder) {
            this.arraySize = arraySize;
            this.baseDecoder = baseDecoder;
        }

        @Override
        public boolean isDynamicType() {
            return this.baseDecoder.isDynamicType();
        }

        @Override
        public int encodedByteLength() {
            return this.baseDecoder.encodedByteLength() * this.arraySize;
        }

        @Override
        public List<ORIGINAL_TYPE> decodeToNative(String input) {
            return decodeArrayToNative(input, this.arraySize, this.baseDecoder);
        }

        @Override
        public Type<List<ORIGINAL_TYPE>> decode(String input) {
            throw new UnsupportedOperationException("decode to list of abi value is not supported.");
        }
    }

    private static class DynamicArrayDecoder<ABI_TYPE extends Type<ORIGINAL_TYPE>, ORIGINAL_TYPE>
            extends AbiTypeDecoder<Type<List<ORIGINAL_TYPE>>, List<ORIGINAL_TYPE>> {

        private final AbiTypeDecoder<ABI_TYPE, ORIGINAL_TYPE> baseDecoder;

        private DynamicArrayDecoder(AbiTypeDecoder<ABI_TYPE, ORIGINAL_TYPE> baseDecoder) {
            this.baseDecoder = baseDecoder;
        }

        @Override
        public boolean isDynamicType() {
            return true;
        }

        @Override
        public List<ORIGINAL_TYPE> decodeToNative(String input) {
            String arraySizeString = input.substring(0, MAX_BYTE_LENGTH_FOR_HEX_STRING);
            UnsignedNumericDecoder<Uint256> uint256Decoder = getUint256Decoder();
            int arraySize = uint256Decoder.decodeToNative(arraySizeString).intValue();

            return decodeArrayToNative(input.substring(MAX_BYTE_LENGTH_FOR_HEX_STRING), arraySize, this.baseDecoder);
        }

        @Override
        public Type<List<ORIGINAL_TYPE>> decode(String input) {
            throw new UnsupportedOperationException("decode to list of abi value is not supported.");
        }
    }

    private static <ABI_TYPE extends Type<ORIGINAL_TYPE>, ORIGINAL_TYPE> List<ORIGINAL_TYPE> decodeArrayToNative(
            String input, int arraySize, AbiTypeDecoder<ABI_TYPE, ORIGINAL_TYPE> baseDecoder) {

        int offset = 0;
        UnsignedNumericDecoder<Uint256> uint256Decoder = getUint256Decoder();

        List<ORIGINAL_TYPE> decodedValues = new ArrayList<>();
        for (int index = 0; index < arraySize; index++) {
            ORIGINAL_TYPE decodedValue;
            int nextOffset;
            if (baseDecoder.isDynamicType()) {
                nextOffset = offset + MAX_BYTE_LENGTH_FOR_HEX_STRING;
                String dataPositionString = input.substring(offset, nextOffset);
                int dataPosition = uint256Decoder.decodeToNative(dataPositionString).intValue();
                decodedValue = baseDecoder.decodeToNative(input.substring(dataPosition << 1));
            } else {
                nextOffset = offset + (baseDecoder.encodedByteLength() << 1);
                decodedValue = baseDecoder.decodeToNative(input.substring(offset, nextOffset));
            }

            decodedValues.add(decodedValue);
            offset = nextOffset;
        }

        return decodedValues;
    }

    static List<?> decodeTupleToNative(String input, List<AbiTypeDecoder<?, ?>> baseDecoders) {

        int offset = 0;
        UnsignedNumericDecoder<Uint256> uint256Decoder = getUint256Decoder();

        List<Object> decodedValues = new ArrayList<>();
        for (AbiTypeDecoder<?, ?> baseDecoder : baseDecoders) {
            Object decodedValue;
            int nextOffset;
            if (baseDecoder.isDynamicType()) {
                nextOffset = offset + MAX_BYTE_LENGTH_FOR_HEX_STRING;
                String dataPositionString = input.substring(offset, nextOffset);
                int dataPosition = uint256Decoder.decodeToNative(dataPositionString).intValue();
                decodedValue = baseDecoder.decodeToNative(input.substring(dataPosition << 1));
            } else {
                nextOffset = offset + (baseDecoder.encodedByteLength() << 1);
                decodedValue = baseDecoder.decodeToNative(input.substring(offset, nextOffset));
            }

            decodedValues.add(decodedValue);
            offset = nextOffset;
        }

        return decodedValues;
    }

    private static Map<String, AbiTypeDecoder<?, ?>> initDecoderMapping() {

        // Uint160 の変換は Address の変換でも利用する
        UnsignedNumericDecoder<Uint160> uint160Decoder = new UnsignedNumericDecoder<>(160) {
            @Override
            public Uint160 decode(String input) {
                return new Uint160(this.decodeToNative(input));
            }
        };

        // 可変長型のサイズ特定に Uint256 の変換処理を利用するので、流用できるようにここで定義する
        UnsignedNumericDecoder<Uint256> uint256Decoder = new UnsignedNumericDecoder<>(256) {
            @Override
            public Uint256 decode(String input) {
                return new Uint256(this.decodeToNative(input));
            }
        };

        // bytes の変換は string の変換でも利用する
        AbiTypeDecoder<DynamicBytes, byte[]> dynamicBytesDecoder = initDynamicBytesDecoder(uint256Decoder);

        Map<String, AbiTypeDecoder<?, ?>> mapping = new HashMap<>();
        mapping.put("bool", initBoolDecoder());
        mapping.put("address", initAddressDecoder(uint160Decoder));
        mapping.put("bytes", dynamicBytesDecoder);
        mapping.put("string", initUtf8StringDecoder(dynamicBytesDecoder));

        mapping.putAll(initStaticBytesDecoderMapping());
        mapping.putAll(initNumericDecoderMapping(uint160Decoder, uint256Decoder));

        return mapping;
    }

    private static AbiTypeDecoder<Bool, Boolean> initBoolDecoder() {
        final Bool TRUE_BOOL = new Bool(true);
        final Bool FALSE_BOOL = new Bool(false);

        // reference from org.web3j.abi.TypeDecoder.decodeBool()
        return new AbiTypeDecoder<>() {

            @Override
            public boolean isDynamicType() {
                return false;
            }

            @Override
            public Boolean decodeToNative(String input) {
                BigInteger numericValue = Numeric.toBigInt(input.substring(0, MAX_BYTE_LENGTH_FOR_HEX_STRING));
                boolean isFalse = numericValue.equals(BigInteger.ZERO);
                return isFalse ? Boolean.FALSE : Boolean.TRUE;
            }

            @Override
            public Bool decode(String input) {
                Boolean value = this.decodeToNative(input);
                return value.booleanValue() ? TRUE_BOOL : FALSE_BOOL;
            }
        };
    }

    private static AbiTypeDecoder<Address, String> initAddressDecoder(UnsignedNumericDecoder<Uint160> uint160Decoder) {
        // reference from org.web3j.abi.TypeDecoder.decodeAddress()
        return new AbiTypeDecoder<>() {

            @Override
            public boolean isDynamicType() {
                return false;
            }

            @Override
            public String decodeToNative(String input) {
                Address value = this.decode(input);
                return value.getValue();
            }

            @Override
            public Address decode(String input) {
                Uint160 value = uint160Decoder.decode(input);
                return new Address(value);
            }
        };
    }

    private static AbiTypeDecoder<DynamicBytes, byte[]> initDynamicBytesDecoder(
            UnsignedNumericDecoder<Uint256> uintDecoder) {

        // reference from org.web3j.abi.TypeDecoder.decodeDynamicBytes()
        return new AbiTypeDecoder<>() {

            @Override
            public boolean isDynamicType() {
                return true;
            }

            @Override
            public byte[] decodeToNative(String input) {
                String hexLength = input.substring(0, MAX_BYTE_LENGTH_FOR_HEX_STRING);
                int encodedLength = uintDecoder.decodeToNative(hexLength).intValue();
                int hexStringEncodedLength = encodedLength << 1;

                String data = input.substring(MAX_BYTE_LENGTH_FOR_HEX_STRING,
                        MAX_BYTE_LENGTH_FOR_HEX_STRING + hexStringEncodedLength);
                return Numeric.hexStringToByteArray(data);
            }

            @Override
            public DynamicBytes decode(String input) {
                return new DynamicBytes(this.decodeToNative(input));
            }
        };
    }

    private static AbiTypeDecoder<Utf8String, String> initUtf8StringDecoder(
            AbiTypeDecoder<DynamicBytes, byte[]> dynamicBytesDecoder) {

        // reference from org.web3j.abi.TypeDecoder.decodeUtf8String()
        return new AbiTypeDecoder<>() {

            @Override
            public boolean isDynamicType() {
                return true;
            }

            @Override
            public String decodeToNative(String input) {
                byte[] bytes = dynamicBytesDecoder.decodeToNative(input);
                return new String(bytes, StandardCharsets.UTF_8);
            }

            @Override
            public Utf8String decode(String input) {
                return new Utf8String(this.decodeToNative(input));
            }
        };
    }

    // reference from org.web3j.abi.TypeDecoder.decodeBytes()
    private static abstract class StaticBytesDecoder<ABI_TYPE extends Type<byte[]>>
            extends AbiTypeDecoder<ABI_TYPE, byte[]> {

        private final int byteLength;

        protected StaticBytesDecoder(int byteLength) {
            this.byteLength = byteLength;
        }

        @Override
        public boolean isDynamicType() {
            return false;
        }

        @Override
        public byte[] decodeToNative(String input) {
            int hexStringLength = this.byteLength << 1;
            return Numeric.hexStringToByteArray(input.substring(0, hexStringLength));
        }
    }

    private static Map<String, AbiTypeDecoder<?, ?>> initStaticBytesDecoderMapping() {
        Map<String, AbiTypeDecoder<?, ?>> mapping = new HashMap<>();

        mapping.put("bytes1", new StaticBytesDecoder<Bytes1>(1) {
            @Override
            public Bytes1 decode(String input) {
                return new Bytes1(this.decodeToNative(input));
            }
        });
        mapping.put("bytes2", new StaticBytesDecoder<Bytes2>(2) {
            @Override
            public Bytes2 decode(String input) {
                return new Bytes2(this.decodeToNative(input));
            }
        });
        mapping.put("bytes3", new StaticBytesDecoder<Bytes3>(3) {
            @Override
            public Bytes3 decode(String input) {
                return new Bytes3(this.decodeToNative(input));
            }
        });
        mapping.put("bytes4", new StaticBytesDecoder<Bytes4>(4) {
            @Override
            public Bytes4 decode(String input) {
                return new Bytes4(this.decodeToNative(input));
            }
        });
        mapping.put("bytes5", new StaticBytesDecoder<Bytes5>(5) {
            @Override
            public Bytes5 decode(String input) {
                return new Bytes5(this.decodeToNative(input));
            }
        });
        mapping.put("bytes6", new StaticBytesDecoder<Bytes6>(6) {
            @Override
            public Bytes6 decode(String input) {
                return new Bytes6(this.decodeToNative(input));
            }
        });
        mapping.put("bytes7", new StaticBytesDecoder<Bytes7>(7) {
            @Override
            public Bytes7 decode(String input) {
                return new Bytes7(this.decodeToNative(input));
            }
        });
        mapping.put("bytes8", new StaticBytesDecoder<Bytes8>(8) {
            @Override
            public Bytes8 decode(String input) {
                return new Bytes8(this.decodeToNative(input));
            }
        });
        mapping.put("bytes9", new StaticBytesDecoder<Bytes9>(9) {
            @Override
            public Bytes9 decode(String input) {
                return new Bytes9(this.decodeToNative(input));
            }
        });
        mapping.put("bytes10", new StaticBytesDecoder<Bytes10>(10) {
            @Override
            public Bytes10 decode(String input) {
                return new Bytes10(this.decodeToNative(input));
            }
        });
        mapping.put("bytes11", new StaticBytesDecoder<Bytes11>(11) {
            @Override
            public Bytes11 decode(String input) {
                return new Bytes11(this.decodeToNative(input));
            }
        });
        mapping.put("bytes12", new StaticBytesDecoder<Bytes12>(12) {
            @Override
            public Bytes12 decode(String input) {
                return new Bytes12(this.decodeToNative(input));
            }
        });
        mapping.put("bytes13", new StaticBytesDecoder<Bytes13>(13) {
            @Override
            public Bytes13 decode(String input) {
                return new Bytes13(this.decodeToNative(input));
            }
        });
        mapping.put("bytes14", new StaticBytesDecoder<Bytes14>(14) {
            @Override
            public Bytes14 decode(String input) {
                return new Bytes14(this.decodeToNative(input));
            }
        });
        mapping.put("bytes15", new StaticBytesDecoder<Bytes15>(15) {
            @Override
            public Bytes15 decode(String input) {
                return new Bytes15(this.decodeToNative(input));
            }
        });
        mapping.put("bytes16", new StaticBytesDecoder<Bytes16>(16) {
            @Override
            public Bytes16 decode(String input) {
                return new Bytes16(this.decodeToNative(input));
            }
        });
        mapping.put("bytes17", new StaticBytesDecoder<Bytes17>(17) {
            @Override
            public Bytes17 decode(String input) {
                return new Bytes17(this.decodeToNative(input));
            }
        });
        mapping.put("bytes18", new StaticBytesDecoder<Bytes18>(18) {
            @Override
            public Bytes18 decode(String input) {
                return new Bytes18(this.decodeToNative(input));
            }
        });
        mapping.put("bytes19", new StaticBytesDecoder<Bytes19>(19) {
            @Override
            public Bytes19 decode(String input) {
                return new Bytes19(this.decodeToNative(input));
            }
        });
        mapping.put("bytes20", new StaticBytesDecoder<Bytes20>(20) {
            @Override
            public Bytes20 decode(String input) {
                return new Bytes20(this.decodeToNative(input));
            }
        });
        mapping.put("bytes21", new StaticBytesDecoder<Bytes21>(21) {
            @Override
            public Bytes21 decode(String input) {
                return new Bytes21(this.decodeToNative(input));
            }
        });
        mapping.put("bytes22", new StaticBytesDecoder<Bytes22>(22) {
            @Override
            public Bytes22 decode(String input) {
                return new Bytes22(this.decodeToNative(input));
            }
        });
        mapping.put("bytes23", new StaticBytesDecoder<Bytes23>(23) {
            @Override
            public Bytes23 decode(String input) {
                return new Bytes23(this.decodeToNative(input));
            }
        });
        mapping.put("bytes24", new StaticBytesDecoder<Bytes24>(24) {
            @Override
            public Bytes24 decode(String input) {
                return new Bytes24(this.decodeToNative(input));
            }
        });
        mapping.put("bytes25", new StaticBytesDecoder<Bytes25>(25) {
            @Override
            public Bytes25 decode(String input) {
                return new Bytes25(this.decodeToNative(input));
            }
        });
        mapping.put("bytes26", new StaticBytesDecoder<Bytes26>(26) {
            @Override
            public Bytes26 decode(String input) {
                return new Bytes26(this.decodeToNative(input));
            }
        });
        mapping.put("bytes27", new StaticBytesDecoder<Bytes27>(27) {
            @Override
            public Bytes27 decode(String input) {
                return new Bytes27(this.decodeToNative(input));
            }
        });
        mapping.put("bytes28", new StaticBytesDecoder<Bytes28>(28) {
            @Override
            public Bytes28 decode(String input) {
                return new Bytes28(this.decodeToNative(input));
            }
        });
        mapping.put("bytes29", new StaticBytesDecoder<Bytes29>(29) {
            @Override
            public Bytes29 decode(String input) {
                return new Bytes29(this.decodeToNative(input));
            }
        });
        mapping.put("bytes30", new StaticBytesDecoder<Bytes30>(30) {
            @Override
            public Bytes30 decode(String input) {
                return new Bytes30(this.decodeToNative(input));
            }
        });
        mapping.put("bytes31", new StaticBytesDecoder<Bytes31>(31) {
            @Override
            public Bytes31 decode(String input) {
                return new Bytes31(this.decodeToNative(input));
            }
        });
        mapping.put("bytes32", new StaticBytesDecoder<Bytes32>(32) {
            @Override
            public Bytes32 decode(String input) {
                return new Bytes32(this.decodeToNative(input));
            }
        });

        return mapping;
    }

    // reference from org.web3j.abi.TypeDecoder.decodeNumeric()
    private static abstract class NumericDecoder<ABI_TYPE extends Type<BigInteger>>
            extends AbiTypeDecoder<ABI_TYPE, BigInteger> {

        private final int byteLength;

        protected NumericDecoder(int bitLength) {
            this.byteLength = bitLength >> 3;
        }

        @Override
        public boolean isDynamicType() {
            return false;
        }

        @Override
        public BigInteger decodeToNative(String input) {
            byte[] inputByteArray = Numeric.hexStringToByteArray(input.substring(0, MAX_BYTE_LENGTH_FOR_HEX_STRING));
            byte[] resultByteArray = new byte[this.byteLength + 1];
            if (this.withSigned()) {
                resultByteArray[0] = inputByteArray[0]; // take MSB as sign bit
            }

            int valueOffset = Type.MAX_BYTE_LENGTH - this.byteLength;
            System.arraycopy(inputByteArray, valueOffset, resultByteArray, 1, this.byteLength);
            return new BigInteger(resultByteArray);
        }

        protected abstract boolean withSigned();
    }

    private static abstract class UnsignedNumericDecoder<ABI_TYPE extends Type<BigInteger>>
            extends NumericDecoder<ABI_TYPE> {

        protected UnsignedNumericDecoder(int bitLength) {
            super(bitLength);
        }

        @Override
        protected boolean withSigned() {
            return false;
        }
    }

    private static abstract class SignedNumericDecoder<ABI_TYPE extends Type<BigInteger>>
            extends NumericDecoder<ABI_TYPE> {

        protected SignedNumericDecoder(int bitLength) {
            super(bitLength);
        }

        @Override
        protected boolean withSigned() {
            return true;
        }
    }

    private static Map<String, AbiTypeDecoder<?, ?>> initNumericDecoderMapping(
            UnsignedNumericDecoder<Uint160> uint160Decoder, UnsignedNumericDecoder<Uint256> uint256Decoder) {

        SignedNumericDecoder<Int256> intDecoder = new SignedNumericDecoder<>(256) {
            @Override
            public Int256 decode(String input) {
                return new Int256(this.decodeToNative(input));
            }
        };

        Map<String, AbiTypeDecoder<?, ?>> mapping = new HashMap<>();
        mapping.put("uint8", new UnsignedNumericDecoder<Uint8>(8) {
            @Override
            public Uint8 decode(String input) {
                return new Uint8(this.decodeToNative(input));
            }
        });
        mapping.put("uint16", new UnsignedNumericDecoder<Uint16>(16) {
            @Override
            public Uint16 decode(String input) {
                return new Uint16(this.decodeToNative(input));
            }
        });
        mapping.put("uint24", new UnsignedNumericDecoder<Uint24>(24) {
            @Override
            public Uint24 decode(String input) {
                return new Uint24(this.decodeToNative(input));
            }
        });
        mapping.put("uint32", new UnsignedNumericDecoder<Uint32>(32) {
            @Override
            public Uint32 decode(String input) {
                return new Uint32(this.decodeToNative(input));
            }
        });
        mapping.put("uint40", new UnsignedNumericDecoder<Uint40>(40) {
            @Override
            public Uint40 decode(String input) {
                return new Uint40(this.decodeToNative(input));
            }
        });
        mapping.put("uint48", new UnsignedNumericDecoder<Uint48>(48) {
            @Override
            public Uint48 decode(String input) {
                return new Uint48(this.decodeToNative(input));
            }
        });
        mapping.put("uint56", new UnsignedNumericDecoder<Uint56>(56) {
            @Override
            public Uint56 decode(String input) {
                return new Uint56(this.decodeToNative(input));
            }
        });
        mapping.put("uint64", new UnsignedNumericDecoder<Uint64>(64) {
            @Override
            public Uint64 decode(String input) {
                return new Uint64(this.decodeToNative(input));
            }
        });
        mapping.put("uint72", new UnsignedNumericDecoder<Uint72>(72) {
            @Override
            public Uint72 decode(String input) {
                return new Uint72(this.decodeToNative(input));
            }
        });
        mapping.put("uint80", new UnsignedNumericDecoder<Uint80>(80) {
            @Override
            public Uint80 decode(String input) {
                return new Uint80(this.decodeToNative(input));
            }
        });
        mapping.put("uint88", new UnsignedNumericDecoder<Uint88>(88) {
            @Override
            public Uint88 decode(String input) {
                return new Uint88(this.decodeToNative(input));
            }
        });
        mapping.put("uint96", new UnsignedNumericDecoder<Uint96>(96) {
            @Override
            public Uint96 decode(String input) {
                return new Uint96(this.decodeToNative(input));
            }
        });
        mapping.put("uint104", new UnsignedNumericDecoder<Uint104>(104) {
            @Override
            public Uint104 decode(String input) {
                return new Uint104(this.decodeToNative(input));
            }
        });
        mapping.put("uint112", new UnsignedNumericDecoder<Uint112>(112) {
            @Override
            public Uint112 decode(String input) {
                return new Uint112(this.decodeToNative(input));
            }
        });
        mapping.put("uint120", new UnsignedNumericDecoder<Uint120>(120) {
            @Override
            public Uint120 decode(String input) {
                return new Uint120(this.decodeToNative(input));
            }
        });
        mapping.put("uint128", new UnsignedNumericDecoder<Uint128>(128) {
            @Override
            public Uint128 decode(String input) {
                return new Uint128(this.decodeToNative(input));
            }
        });
        mapping.put("uint136", new UnsignedNumericDecoder<Uint136>(136) {
            @Override
            public Uint136 decode(String input) {
                return new Uint136(this.decodeToNative(input));
            }
        });
        mapping.put("uint144", new UnsignedNumericDecoder<Uint144>(144) {
            @Override
            public Uint144 decode(String input) {
                return new Uint144(this.decodeToNative(input));
            }
        });
        mapping.put("uint152", new UnsignedNumericDecoder<Uint152>(152) {
            @Override
            public Uint152 decode(String input) {
                return new Uint152(this.decodeToNative(input));
            }
        });
        mapping.put("uint160", uint160Decoder);
        mapping.put("uint168", new UnsignedNumericDecoder<Uint168>(168) {
            @Override
            public Uint168 decode(String input) {
                return new Uint168(this.decodeToNative(input));
            }
        });
        mapping.put("uint176", new UnsignedNumericDecoder<Uint176>(176) {
            @Override
            public Uint176 decode(String input) {
                return new Uint176(this.decodeToNative(input));
            }
        });
        mapping.put("uint184", new UnsignedNumericDecoder<Uint184>(184) {
            @Override
            public Uint184 decode(String input) {
                return new Uint184(this.decodeToNative(input));
            }
        });
        mapping.put("uint192", new UnsignedNumericDecoder<Uint192>(192) {
            @Override
            public Uint192 decode(String input) {
                return new Uint192(this.decodeToNative(input));
            }
        });
        mapping.put("uint200", new UnsignedNumericDecoder<Uint200>(200) {
            @Override
            public Uint200 decode(String input) {
                return new Uint200(this.decodeToNative(input));
            }
        });
        mapping.put("uint208", new UnsignedNumericDecoder<Uint208>(208) {
            @Override
            public Uint208 decode(String input) {
                return new Uint208(this.decodeToNative(input));
            }
        });
        mapping.put("uint216", new UnsignedNumericDecoder<Uint216>(216) {
            @Override
            public Uint216 decode(String input) {
                return new Uint216(this.decodeToNative(input));
            }
        });
        mapping.put("uint224", new UnsignedNumericDecoder<Uint224>(224) {
            @Override
            public Uint224 decode(String input) {
                return new Uint224(this.decodeToNative(input));
            }
        });
        mapping.put("uint232", new UnsignedNumericDecoder<Uint232>(232) {
            @Override
            public Uint232 decode(String input) {
                return new Uint232(this.decodeToNative(input));
            }
        });
        mapping.put("uint240", new UnsignedNumericDecoder<Uint240>(240) {
            @Override
            public Uint240 decode(String input) {
                return new Uint240(this.decodeToNative(input));
            }
        });
        mapping.put("uint248", new UnsignedNumericDecoder<Uint248>(248) {
            @Override
            public Uint248 decode(String input) {
                return new Uint248(this.decodeToNative(input));
            }
        });
        mapping.put("uint256", uint256Decoder);
        mapping.put("uint", uint256Decoder);

        mapping.put("int8", new SignedNumericDecoder<Int8>(8) {
            @Override
            public Int8 decode(String input) {
                return new Int8(this.decodeToNative(input));
            }
        });
        mapping.put("int16", new SignedNumericDecoder<Int16>(16) {
            @Override
            public Int16 decode(String input) {
                return new Int16(this.decodeToNative(input));
            }
        });
        mapping.put("int24", new SignedNumericDecoder<Int24>(24) {
            @Override
            public Int24 decode(String input) {
                return new Int24(this.decodeToNative(input));
            }
        });
        mapping.put("int32", new SignedNumericDecoder<Int32>(32) {
            @Override
            public Int32 decode(String input) {
                return new Int32(this.decodeToNative(input));
            }
        });
        mapping.put("int40", new SignedNumericDecoder<Int40>(40) {
            @Override
            public Int40 decode(String input) {
                return new Int40(this.decodeToNative(input));
            }
        });
        mapping.put("int48", new SignedNumericDecoder<Int48>(48) {
            @Override
            public Int48 decode(String input) {
                return new Int48(this.decodeToNative(input));
            }
        });
        mapping.put("int56", new SignedNumericDecoder<Int56>(56) {
            @Override
            public Int56 decode(String input) {
                return new Int56(this.decodeToNative(input));
            }
        });
        mapping.put("int64", new SignedNumericDecoder<Int64>(64) {
            @Override
            public Int64 decode(String input) {
                return new Int64(this.decodeToNative(input));
            }
        });
        mapping.put("int72", new SignedNumericDecoder<Int72>(72) {
            @Override
            public Int72 decode(String input) {
                return new Int72(this.decodeToNative(input));
            }
        });
        mapping.put("int80", new SignedNumericDecoder<Int80>(80) {
            @Override
            public Int80 decode(String input) {
                return new Int80(this.decodeToNative(input));
            }
        });
        mapping.put("int88", new SignedNumericDecoder<Int88>(88) {
            @Override
            public Int88 decode(String input) {
                return new Int88(this.decodeToNative(input));
            }
        });
        mapping.put("int96", new SignedNumericDecoder<Int96>(96) {
            @Override
            public Int96 decode(String input) {
                return new Int96(this.decodeToNative(input));
            }
        });
        mapping.put("int104", new SignedNumericDecoder<Int104>(104) {
            @Override
            public Int104 decode(String input) {
                return new Int104(this.decodeToNative(input));
            }
        });
        mapping.put("int112", new SignedNumericDecoder<Int112>(112) {
            @Override
            public Int112 decode(String input) {
                return new Int112(this.decodeToNative(input));
            }
        });
        mapping.put("int120", new SignedNumericDecoder<Int120>(120) {
            @Override
            public Int120 decode(String input) {
                return new Int120(this.decodeToNative(input));
            }
        });
        mapping.put("int128", new SignedNumericDecoder<Int128>(128) {
            @Override
            public Int128 decode(String input) {
                return new Int128(this.decodeToNative(input));
            }
        });
        mapping.put("int136", new SignedNumericDecoder<Int136>(136) {
            @Override
            public Int136 decode(String input) {
                return new Int136(this.decodeToNative(input));
            }
        });
        mapping.put("int144", new SignedNumericDecoder<Int144>(144) {
            @Override
            public Int144 decode(String input) {
                return new Int144(this.decodeToNative(input));
            }
        });
        mapping.put("int152", new SignedNumericDecoder<Int152>(152) {
            @Override
            public Int152 decode(String input) {
                return new Int152(this.decodeToNative(input));
            }
        });
        mapping.put("int160", new SignedNumericDecoder<Int160>(160) {
            @Override
            public Int160 decode(String input) {
                return new Int160(this.decodeToNative(input));
            }
        });
        mapping.put("int168", new SignedNumericDecoder<Int168>(168) {
            @Override
            public Int168 decode(String input) {
                return new Int168(this.decodeToNative(input));
            }
        });
        mapping.put("int176", new SignedNumericDecoder<Int176>(176) {
            @Override
            public Int176 decode(String input) {
                return new Int176(this.decodeToNative(input));
            }
        });
        mapping.put("int184", new SignedNumericDecoder<Int184>(184) {
            @Override
            public Int184 decode(String input) {
                return new Int184(this.decodeToNative(input));
            }
        });
        mapping.put("int192", new SignedNumericDecoder<Int192>(192) {
            @Override
            public Int192 decode(String input) {
                return new Int192(this.decodeToNative(input));
            }
        });
        mapping.put("int200", new SignedNumericDecoder<Int200>(200) {
            @Override
            public Int200 decode(String input) {
                return new Int200(this.decodeToNative(input));
            }
        });
        mapping.put("int208", new SignedNumericDecoder<Int208>(208) {
            @Override
            public Int208 decode(String input) {
                return new Int208(this.decodeToNative(input));
            }
        });
        mapping.put("int216", new SignedNumericDecoder<Int216>(216) {
            @Override
            public Int216 decode(String input) {
                return new Int216(this.decodeToNative(input));
            }
        });
        mapping.put("int224", new SignedNumericDecoder<Int224>(224) {
            @Override
            public Int224 decode(String input) {
                return new Int224(this.decodeToNative(input));
            }
        });
        mapping.put("int232", new SignedNumericDecoder<Int232>(232) {
            @Override
            public Int232 decode(String input) {
                return new Int232(this.decodeToNative(input));
            }
        });
        mapping.put("int240", new SignedNumericDecoder<Int240>(240) {
            @Override
            public Int240 decode(String input) {
                return new Int240(this.decodeToNative(input));
            }
        });
        mapping.put("int248", new SignedNumericDecoder<Int248>(248) {
            @Override
            public Int248 decode(String input) {
                return new Int248(this.decodeToNative(input));
            }
        });
        mapping.put("int256", intDecoder);
        mapping.put("int", intDecoder);

        return mapping;
    }
}

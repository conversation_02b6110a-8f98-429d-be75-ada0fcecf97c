package com.decurret_dcp.dcjpy.bcclient.base.transaction;

import java.util.concurrent.atomic.AtomicReference;

import com.decurret_dcp.dcjpy.bcclient.base.credential.Web3jCredentialHolder;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NonceManagerHolder {

    private final MainWebSocketConnectionPool connectionPool;

    private final AtomicReference<NonceManager> reference;

    NonceManagerHolder(MainWebSocketConnectionPool connectionPool, NonceManager nonceManager) {
        this.connectionPool = connectionPool;
        this.reference = new AtomicReference<>(nonceManager);
    }

    public static NonceManagerHolder create(MainWebSocketConnectionPool connectionPool) {
        Web3jCredentialHolder credentialHolder = new Web3jCredentialHolder();
        NonceManager nonceManager = NonceManager.create(connectionPool, credentialHolder);

        log.info("Create NonceManager with nonceKey : {}", nonceManager.getNonceKey());

        return new NonceManagerHolder(connectionPool, nonceManager);
    }

    public NonceManager getNonceManager() {
        return this.reference.get();
    }

    public void refreshCredential() {
        Web3jCredentialHolder credentialHolder = new Web3jCredentialHolder();
        NonceManager nonceManager = NonceManager.create(this.connectionPool, credentialHolder);

        log.warn("Refresh NonceManager with nonceKey : {}", nonceManager.getNonceKey());

        this.reference.set(nonceManager);
    }
}

package com.decurret_dcp.dcjpy.bcclient.base.contract

import spock.lang.Specification

class FinancialContractSpec extends Specification {

    def "Contains: 正常に判定ができること"() {
        when:
        boolean result = FinancialContract.mustFinancial(contractName)

        then:
        result == bool

        where:
        contractName           || bool
        "FinancialCheck"       || true
        "FinancialZoneAccount" || true
        "Issuer"               || true
        "None"                 || false
        "Token"                || false
    }
}

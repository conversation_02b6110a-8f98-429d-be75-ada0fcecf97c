package com.decurret_dcp.dcjpy.bcclient.base.adaptor

import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractAbi
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractAbiConvertUtil
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractFunction
import com.decurret_dcp.dcjpy.bcclient.base.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcclient.base.properties.AbiFormat
import com.decurret_dcp.dcjpy.bcclient.base.properties.DynamoDbProperty
import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthTransactionCommand
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import spock.lang.Specification

@Testcontainers
class AwsDynamoDbAdaptorSpec extends Specification {

    static DynamoDbClient dynamoDbClient

    static DynamoDbProperty dynamoDbProperty

    static AwsDynamoDbAdaptor awsDynamoDbAdaptor

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
    }

    def setupSpec() {
        String localStackPort = AdhocHelper.getLocalStackPort()
        dynamoDbClient = DynamoDbClient.builder()
                .region(Region.AP_NORTHEAST_1) // 適切なリージョンを指定
                .endpointOverride(URI.create("http://localhost:${localStackPort}")) // DynamoDB Localのエンドポイントを指定
                .build()
        awsDynamoDbAdaptor = new AwsDynamoDbAdaptor(dynamoDbClient, dynamoDbProperty)
        if (!AdhocHelper.tableExists(dynamoDbClient, "transaction_queue")) {
            AdhocHelper.createDynamoDBTable(dynamoDbClient)
        }

    }

    def cleanupSpec() {
        dynamoDbClient.close()
        AdhocHelper.cleanupSpec()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }

    def "testRegister_データが登録できていること"() {
        setup:
        ClassLoader classLoader = getClass().getClassLoader()
        File providerJson = new File(classLoader.getResource("Provider.json").getFile())

        Map<String, byte[]> contentMap = Map.ofEntries(
                Map.entry("Provider", providerJson.getBytes())
        )

        def content = contentMap.get("Provider")
        ContractAbi abi = ContractAbiConvertUtil.convertByteToObject(AbiFormat.HARDHAT, "Provider", content)
        ContractFunction function = abi.getFunction("addProviderRole")
        Map<String, Object> parameters = Map.ofEntries(
                Map.entry("zoneId", "3001")
        )

        EthTransactionCommand command = EthTransactionCommand.builder()
                .requestId("123456")
                .zoneId(ZoneId.of("3001"))
                .contractAbi(abi)
                .contractFunction(function)
                .parameters(parameters)
                .traceId("11111")
                .build()

        when:
        awsDynamoDbAdaptor.register(command)

        then:
        Map<String, AttributeValue> record = AdhocHelper.getTransactionQueue(dynamoDbClient, "123456")
        record.get("request_id").s().equals("123456")

        record.get("args").m().keySet().size() == 1
        record.get("args").m().keySet().contains("zoneId")
        record.get("args").m().values().size() == 1
        record.get("args").m().values().getAt(0).s().equals("3001")

        record.get("zone_id").n().equals("3001")
        record.get("transaction_status").s().equals("queuing")
        !Objects.isNull(record.get("expires_at").n())
        record.get("method").s().equals("addProviderRole")
        record.get("contract_name").s().equals("Provider")
        record.get("request_id").s().equals("123456")

        cleanup:
        AdhocHelper.deleteTransactionQueue(dynamoDbClient, "123456")
    }


}

package com.decurret_dcp.dcjpy.bcclient.base.util

import com.decurret_dcp.dcjpy.bcclient.base.util.EthDataTypeUtil
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.*
import org.web3j.abi.datatypes.generated.*
import org.web3j.abi.datatypes.primitive.Byte
import org.web3j.abi.datatypes.primitive.Float
import spock.lang.Specification

class EthDataTypeUtilSpec extends Specification {
    def "ToTypeClass: 型文字列をAbiTypesに変換できること"() {
        when:
        Object result = EthDataTypeUtil.toTypeClass(str)

        then:
        result == expected

        where:
        str       || expected
        "address" || Address.class
        "bool"    || Bool.class
        "boolean" || Bool.class
        "string"  || Utf8String.class
        "bytes"   || DynamicBytes.class
        "bool[]"  || DynamicArray.class
        "bytes32" || Bytes32.class
        "bytes10" || Bytes10.class
        "byte"    || Byte.class
        "float"   || Float.class
        "int"     || Int.class
        "uint"    || Uint.class
        "int8"    || Int8.class
        "uint8"   || Uint8.class
    }

    def "ToTypeClass: 型文字列がnullとから文字のときにIllegalArgumentExceptionが発生すること"() {
        when:
        EthDataTypeUtil.toTypeClass(str)

        then:
        thrown(expected)

        where:
        str  || expected
        null || IllegalArgumentException
        ""   || IllegalArgumentException
        " "  || IllegalArgumentException
        "　"  || IllegalArgumentException
        ""   || IllegalArgumentException
    }

    def "ToTypeClass: Web3jで未対応の型はUnsupportedOperationExceptionが発生すること"() {
        when:
        EthDataTypeUtil.toTypeClass(str)

        then:
        thrown(expected)

        where:
        str         || expected
        "bytes64"   || UnsupportedOperationException
        "bignumber" || UnsupportedOperationException
    }

    def "ToTypeReference: 配列の型文字列をTypeReferenceに変換できること"() {
        when:
        TypeReference result = EthDataTypeUtil.toTypeReference(str)

        then:
        result.getClassType() == expected.getClassType()
        result.getClassType().getGenericSuperclass() == expected.getClassType().getGenericSuperclass()

        where:
        str         || expected
        "uint256[]" || new TypeReference<DynamicArray<Uint256>>() {}
        "address[]" || new TypeReference<DynamicArray<Address>>() {}
        "bytes[]"   || new TypeReference<DynamicArray<Bytes>>() {}
        "bytes32[]" || new TypeReference<DynamicArray<Bytes32>>() {}
        "string[]"  || new TypeReference<DynamicArray<Utf8String>>() {}
        "bool[]"    || new TypeReference<DynamicArray<Bool>>() {}
        "boolean[]" || new TypeReference<DynamicArray<Bool>>() {}
        "bytes10[]" || new TypeReference<DynamicArray<Bytes10>>() {}
        "byte[]"    || new TypeReference<DynamicArray<Byte>>() {}
        "uint[]"    || new TypeReference<DynamicArray<Uint>>() {}
        "int8[]"    || new TypeReference<DynamicArray<Int8>>() {}
        "uint8[]"   || new TypeReference<DynamicArray<Uint8>>() {}
    }

    def "ToTypeReference: 型文字列をTypeReferenceに変換できること"() {
        when:
        TypeReference result = EthDataTypeUtil.toTypeReference(str)

        then:
        result.getClassType() == expected.getClassType()
        result.getType() == expected.getType()

        where:
        str       || expected
        "uint256" || new TypeReference<Uint256>() {}
        "address" || new TypeReference<Address>() {}
        "bytes"   || new TypeReference<DynamicBytes>() {}
        "bytes32" || new TypeReference<Bytes32>() {}
        "string"  || new TypeReference<Utf8String>() {}
        "bool"    || new TypeReference<Bool>() {}
        "boolean" || new TypeReference<Bool>() {}
        "bytes10" || new TypeReference<Bytes10>() {}
        "byte"    || new TypeReference<Byte>() {}
        "float"   || new TypeReference<Float>() {}
        "int"     || new TypeReference<Int>() {}
        "uint"    || new TypeReference<Uint>() {}
        "int8"    || new TypeReference<Int8>() {}
        "uint8"   || new TypeReference<Uint8>() {}
    }

    def "ToStringValue: 文字列変換"() {
        when:
        String result = EthDataTypeUtil.toStringValue(type)

        then:
        result == expected

        where:
        type                                                                                                                      || expected
        new Bool(false)                                                                                                           || "false"
        new Bool(true)                                                                                                            || "true"
        new Bytes(32, (byte[]) [49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]) || "0x3100000000000000000000000000000000000000000000000000000000000000"
        new Bytes10((byte[]) [49, 0, 0, 0, 0, 0, 0, 0, 0, 0])                                                                     || "0x31000000000000000000"
        new Bytes32((byte[]) [49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])   || "0x3100000000000000000000000000000000000000000000000000000000000000"
        new Uint256(1000)                                                                                                         || "1000"
        new Uint256(0)                                                                                                            || "0"
        new Address(32, BigInteger.valueOf(1024))                                                                                 || "0x00000400"
        new Address(32, BigInteger.valueOf(2048))                                                                                 || "0x00000800"
        new Utf8String("0x00001100")                                                                                              || "0x00001100"
        new Float(1.5f)                                                                                                           || "1.5"
        new Int(BigInteger.valueOf(5))                                                                                            || "5"
        new Uint(BigInteger.valueOf(8))                                                                                           || "8"
        new Int8(2)                                                                                                               || "2"
        new Uint8(3)                                                                                                              || "3"
        new Byte((byte) 4)                                                                                                        || "4"
    }

    def "IsArray: Arrayの判定が正常にできること"() {
        when:
        boolean result = EthDataTypeUtil.isArray(str)

        then:
        result == expected

        where:
        str        || expected
        "bool"     || false
        "string"   || false
        "bool[]"   || true
        "string[]" || true
    }

    def "IsBool: Booleanの判定が正常にできること"() {
        when:
        boolean result = EthDataTypeUtil.isBool(str)

        then:
        result == expected

        where:
        str         || expected
        "string"    || false
        "bool"      || true
        "boolean"   || true
        "bool[]"    || true
        "boolean[]" || true
    }

    def "IsBool: 非対応の型の場合にUnsupportedOperationExceptionが発生すること"() {
        when:
        EthDataTypeUtil.isBool("str")

        then:
        thrown(UnsupportedOperationException)
    }
}

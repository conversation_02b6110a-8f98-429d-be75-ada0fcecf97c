package com.decurret_dcp.dcjpy.bcclient.base.websocket

import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty
import com.decurret_dcp.dcjpy.bcclient.base.util.WebSocketUtil
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.web3j.protocol.Web3j
import org.web3j.protocol.websocket.WebSocketService
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.notNull
import static org.mockito.Mockito.mockStatic

class SubWebSocketConnectionPoolSpec extends Specification {

    private static MockedStatic<WebSocketUtil> mocked

    private static ApplicationProperty properties

    def "setupSpec"() {
        WebSocketService service1 = Mockito.mock(WebSocketService.class)
        WebSocketService service2 = Mockito.mock(WebSocketService.class)

        properties = new ApplicationProperty();
        properties.setSubWebSocketUriHost("127.0.0.1")
        properties.setSubWebSocketUriPort("1111")

        mocked = mockStatic(WebSocketUtil.class)
        mocked.when(WebSocketUtil.generateWebSocketService((String) notNull(), (String) notNull(), (boolean) notNull()))
                .thenReturn(service1)
                .thenReturn(service2)
    }

    def "cleanupSpec"() {
        mocked.close()
    }

    def "CreateWebSocketConnection: WebSocketへの接続を2回実行した場合にコネクションが入れ替わること"() {
        when:
        SubWebSocketConnectionPool webSocketConnectionPool = new SubWebSocketConnectionPool(properties)

        // 1回目
        webSocketConnectionPool.createWebSocketConnection()
        Web3j webSocketService1 = webSocketConnectionPool.getWebSocketConnection()
        // 2回目
        webSocketConnectionPool.createWebSocketConnection()
        Web3j webSocketService2 = webSocketConnectionPool.getWebSocketConnection()

        then:
        webSocketService1.equals(webSocketService2) == false
    }

    def "GetWebSocketConnection: 初期化していない状態で取得しようとした場合にIllegalStateExceptionが発生すること"() {
        when:
        SubWebSocketConnectionPool webSocketConnectionPool = new SubWebSocketConnectionPool(properties)
        webSocketConnectionPool.getWebSocketConnection()

        then:
        thrown(IllegalStateException)
    }

    def "IsNewHeadsSubscriptionDisposed: nullの場合にtrueが返ること"() {
        when:
        SubWebSocketConnectionPool webSocketConnectionPool = new SubWebSocketConnectionPool(properties)

        then:
        webSocketConnectionPool.isNewHeadsSubscriptionDisposed() == true
    }
}

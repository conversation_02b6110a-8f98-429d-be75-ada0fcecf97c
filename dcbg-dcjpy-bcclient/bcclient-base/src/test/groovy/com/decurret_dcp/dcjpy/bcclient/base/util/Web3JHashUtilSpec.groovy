package com.decurret_dcp.dcjpy.bcclient.base.util

import com.decurret_dcp.dcjpy.bcclient.base.util.Web3jHashUtil
import org.web3j.abi.datatypes.Bool
import org.web3j.abi.datatypes.Uint
import org.web3j.abi.datatypes.Utf8String
import org.web3j.crypto.Credentials
import spock.lang.Specification

class Web3JHashUtilSpec extends Specification {
    private static final long NOW = 1612167545L
    private static final String HEX_CODE = "******************************************"

    def "GenerateHash: ハッシュ化が実行されること"() {
        when:
        String hash = Web3jHashUtil.generateHash(*params)

        then:
        hash == expected

        where:
        params                                                                       || expected
        [new Uint(BigInteger.valueOf(1))]                                            || "0xb10e2d527612073b26eecdfd717e6a320cf44b4afac2b0732d9fcbe2b7fa0cf6"
        [new Uint(BigInteger.valueOf(1)), new Uint(BigInteger.valueOf(NOW))]         || "0x768f39f7b990b8d72468d281b2ce01f15641a196cc1ff6171b2e1880ce3655d7"
        [new Utf8String("hello"), new Bool(true), new Uint(BigInteger.valueOf(NOW))] || "0xfd8ba98cb7b6f459d6969b1585172c5570bc42f837b1d8e93893d48e3318b31b"
    }

    def "GenerateCredentials: クレデンシャルが生成されること"() {
        when:
        Credentials credentials = Web3jHashUtil.generateCredentials()

        then:
        credentials.getEcKeyPair().getPrivateKey() != null
        credentials.getEcKeyPair().getPublicKey() != null
        credentials.getAddress() != null
        credentials.getAddress().isBlank() == false
    }

    def "Sign: サイン済みハッシュが生成されること"() {
        when:
        String hash = Web3jHashUtil.generateHash(*params)
        String signed = Web3jHashUtil.sign(hash, Credentials.create(HEX_CODE))

        then:
        signed == expected

        where:
        params                                                                       || expected
        [new Uint(BigInteger.valueOf(1))]                                            || "0x92caf5cebf60e1e38c3c90b53b70cb628dcba00a963b2439dc355a5c3b9281f618e7a83e91d8652c790b3d100cee9f23a8114fcafa921f0e7c92bee4d561417c1c"
        [new Uint(BigInteger.valueOf(1)), new Uint(BigInteger.valueOf(NOW))]         || "0x139c3e978fd6cb1e53bda107c009c0c98e218e741bda589ede9bbf945c344d5002d2ecadf01fe15c0fa35a0122aaf0a62d5092a3a923ff3f5e57cf6c91e333b51c"
        [new Utf8String("hello"), new Bool(true), new Uint(BigInteger.valueOf(NOW))] || "0x440af6d522b3bc2a2a6f968c1a0e1b9b2c185d60152035e131893a261327b49e0f19b20f077623ee6082d1bb9b342c1f039714c6709cca8eeb827f551949b6fa1b"
    }
}

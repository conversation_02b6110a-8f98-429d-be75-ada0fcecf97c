package com.decurret_dcp.dcjpy.bcclient.base.transaction

import org.web3j.crypto.Credentials
import spock.lang.Specification

import java.util.concurrent.ConcurrentLinkedQueue

class TransactionManagerSpec extends Specification {

    private static final int TASK_NUM = 1000

    def "GetNonce: 重複なしのNonceがスレッドセーフで取得できること"() {
        when:
        NonceManager nonceManager = new NonceManager(Credentials.create("1"), BigInteger.ZERO)

        Queue<BigInteger> queue = new ConcurrentLinkedQueue<BigInteger>()
        //スレッド生成、実行
        def th = new Thread[TASK_NUM]
        for (int i = 0; i < TASK_NUM; i++) {
            th[i] = new Thread({ ->
                queue.add(nonceManager.getNonce())
            })
            th[i].start()
        }
        //スレッド終了まで待機
        for (int i = 0; i < TASK_NUM; i++) {
            th[i].join()
        }

        then:
        queue.size() == TASK_NUM
        queue.stream().distinct().collect().size() == TASK_NUM
        queue.min() == 0
        queue.max() == TASK_NUM - 1
    }
}

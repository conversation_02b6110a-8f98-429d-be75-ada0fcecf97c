package com.decurret_dcp.dcjpy.bcclient.base.adaptor

import com.decurret_dcp.dcjpy.bcclient.base.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcclient.base.properties.S3Property
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.AwsCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.CreateBucketRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.model.S3Exception
import spock.lang.Specification

import java.nio.file.Files
import java.nio.file.Paths
import java.util.concurrent.TimeUnit

@Testcontainers
class AwsS3AdaptorSpec extends Specification {

    static S3Client s3Client

    static AwsS3Adaptor awsS3Adaptor

    static S3Property s3Property

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
    }

    def setupSpec() {
        String localStackPort = AdhocHelper.getLocalStackPort()
        AwsCredentials credentials = AwsBasicCredentials.create("access123", "secret123")
        s3Client = S3Client.builder()
                .region(Region.AP_NORTHEAST_1)
                .endpointOverride(URI.create("http://localhost:4566"))
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .build()

        s3Property = S3Property.builder()
                .contractBucketName("abijson-local-bucket")
                .externalContractBucketName("external-abijson-local-bucket")
                .localEndpoint("http://localhost")
                .localEndpointPort("4566").build()

        awsS3Adaptor = new AwsS3Adaptor(s3Property)
        createBucket()
    }

    def "Get 1500 files from S3"() {

        given:
        String bucketName = "abijson-local-bucket"

        waitForBucketsExist(bucketName)

        String resourceContent = new String(Files.readAllBytes(
                Paths.get(getClass().getResource("/Test.json").toURI())), "UTF-8")

        when:
        (0..<1500).each { i ->
            String key = "test-file-${i}.json"
            String content = resourceContent
            uploadFile(s3Client, bucketName, key, content)
        }

        then:
        Map<String, byte[]> abiContents = awsS3Adaptor.fetchAbiContents(bucketName, (key) -> key.endsWith(".json"))
        abiContents.size() == 1500
    }

    private static void createBucket() {
        CreateBucketRequest createBucketRequest = CreateBucketRequest.builder()
                .bucket("abijson-local-bucket")
                .build() as CreateBucketRequest
        s3Client.createBucket(createBucketRequest)
    }

    private static void waitForBucketsExist(String bucketName) {
        long startTime = System.currentTimeMillis()
        long timeoutMillis = TimeUnit.SECONDS.toMillis(30)
        boolean exist = false

        while (!exist && System.currentTimeMillis() - startTime < timeoutMillis) {
            exist = true

            try {
                s3Client.headBucket { it.bucket(bucketName) }
            } catch (S3Exception e) {
                exist = false
            }

            if (!exist) {
                try {
                    Thread.sleep(1000)
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt()
                }
            }
        }
    }

    private static void uploadFile(S3Client s3Client, String bucketName, String key, String content) {
        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .build() as PutObjectRequest
        s3Client.putObject(putObjectRequest, RequestBody.fromString(content))
    }
}

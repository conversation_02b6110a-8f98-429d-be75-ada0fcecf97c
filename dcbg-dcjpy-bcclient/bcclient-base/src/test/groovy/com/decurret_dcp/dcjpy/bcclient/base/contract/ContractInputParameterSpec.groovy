package com.decurret_dcp.dcjpy.bcclient.base.contract

import org.web3j.abi.datatypes.DynamicArray
import org.web3j.abi.datatypes.StaticArray
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.Utf8String
import org.web3j.abi.datatypes.generated.Uint256
import spock.lang.Specification

class ContractInputParameterSpec extends Specification {

    def "create: 動的配列型のContractInputParameterが正常に生成できること"() {
        when:
        ContractInputParameter parameter = ContractInputParameter.create("testParam", solidityType)
        def converter = parameter.getConverter()
        def value = converter.toAbiType(inputValue)

        then:
        parameter.getName() == "testParam"
        parameter.getSolidityType() == solidityType
        converter.isDynamic() == isDynamic
        value.getClass() == expectedClass
        value.getValue().size() == expectedSize

        where:
        solidityType  | inputValue                | isDynamic | expectedClass | expectedSize
        "string[]"    | ["test1", "test2"]        | true      | DynamicArray  | 2
        "uint256[]"   | [1, 2, 3]                 | true      | DynamicArray  | 3
        "uint256[2]"  | [1, 2]                    | false     | StaticArray   | 2
        "string[][]"  | [["test1"], ["test2"]]    | true      | DynamicArray  | 2
    }

    def "create: 空の配列型のContractInputParameterが正常に生成できること"() {
        when:
        ContractInputParameter parameter = ContractInputParameter.create("testParam", solidityType)
        def converter = parameter.getConverter()
        def value = converter.toAbiType(inputValue)

        then:
        parameter.getName() == "testParam"
        parameter.getSolidityType() == solidityType
        converter.isDynamic() == isDynamic
        value.getClass() == expectedClass
        value.getValue().size() == expectedSize

        where:
        solidityType  | inputValue | isDynamic | expectedClass | expectedSize
        "string[]"    | []         | true      | DynamicArray  | 0
        "uint256[]"   | []         | true      | DynamicArray  | 0
        "uint256[0]"  | []         | false     | StaticArray   | 0
    }

    def "create: 複数次元の配列型のContractInputParameterが正常に生成できること"() {
        when:
        ContractInputParameter parameter = ContractInputParameter.create("testParam", solidityType)
        def converter = parameter.getConverter()

        then:
        parameter.getName() == "testParam"
        parameter.getSolidityType() == solidityType
        converter.isDynamic() == isDynamic

        where:
        solidityType     | isDynamic
        "string[][]"     | true
        "uint256[][]"    | true
        "uint256[][2]"   | true
        "uint256[2][]"   | true
        "uint256[2][3]"  | false
    }
}

package com.decurret_dcp.dcjpy.bcclient.base.helper

import org.testcontainers.containers.DockerComposeContainer
import org.testcontainers.containers.wait.strategy.Wait
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*

class AdhocHelper {

    private static localStackPort

    private static DockerComposeContainer composeContainer

    static String getDbPort() { return dbPort }

    static String getLocalStackPort() { return localStackPort }

    static {
        startContainer()
    }

    private static void startContainer() {
        // dbが起動した後にpostgresにアクセスできるように、Wait.forLogMessage()でアクセス可能になるログが出力されるまで待つ
        composeContainer = new DockerComposeContainer(new File("../docker-compose.yml"))
                .withExposedService("localstack", 4566)
                .waitingFor("localstack", Wait.forListeningPort())

        composeContainer.start()
        localStackPort = String.valueOf(composeContainer.getServicePort("localstack", 4566))
    }


    static void cleanupSpec() {
        //dynamoDbClient.close()
    }

    static void createDynamoDBTable(DynamoDbClient dynamoDbClient) {
        CreateTableRequest createTableRequest = CreateTableRequest.builder()
                .tableName("transaction_queue") // テーブル名を指定
                .keySchema(
                        KeySchemaElement.builder().attributeName("request_id").keyType(KeyType.HASH).build()
                )
                .attributeDefinitions(
                        AttributeDefinition.builder().attributeName("request_id").attributeType(ScalarAttributeType.S).build()
                )
                .provisionedThroughput(ProvisionedThroughput.builder().readCapacityUnits(5).writeCapacityUnits(5).build())
                .build() as CreateTableRequest

        dynamoDbClient.createTable(createTableRequest)
    }

    static boolean tableExists(DynamoDbClient dynamoDbClient, String tableName) {
        DescribeTableRequest request = DescribeTableRequest.builder()
                .tableName(tableName)
                .build()

        try {
            def result = dynamoDbClient.describeTable(request)
            if (Objects.isNull(result)) {
                return false
            }
            return true
        } catch (ResourceNotFoundException ex) {
            return true
        }


    }

    static Map<String, AttributeValue> getTransactionQueue(DynamoDbClient dynamoDbClient, String requestId) {

        Map<String, AttributeValue> key = [
                'request_id': AttributeValue.builder().s(requestId).build()
        ]

        GetItemRequest getItemRequest = GetItemRequest.builder()
                .tableName('transaction_queue') // テーブル名を指定
                .key(key)
                .build()

        return dynamoDbClient.getItem(getItemRequest).item()
    }

    static void deleteTransactionQueue(DynamoDbClient dynamoDbClient, String requestId) {
        Map<String, AttributeValue> key = [
                'request_id': AttributeValue.builder().s(requestId).build()
        ]

        DeleteItemRequest deleteItemRequest = DeleteItemRequest.builder()
                .tableName('transaction_queue')
                .key(key)
                .build()

        dynamoDbClient.deleteItem(deleteItemRequest)

        println("Item deleted successfully.")
    }
}

package com.decurret_dcp.dcjpy.bcclient.base.contract

import com.decurret_dcp.dcjpy.bcclient.base.properties.AbiFormat
import spock.lang.Specification

class ContractAbiConvertUtilSpec extends Specification {

    private static final Map<String, byte[]> contentMap = new HashMap<>()

    def setupSpec() {
        ClassLoader classLoader = getClass().getClassLoader()
        File providerJson = new File(classLoader.getResource("Provider.json").getFile())
        File validatorJson = new File(classLoader.getResource("Validator.json").getFile())
        File tupleTypesJson = new File(classLoader.getResource("TupleTypes.json").getFile())

        contentMap.put("Provider", providerJson.getBytes())
        contentMap.put("Validator", validatorJson.getBytes())
        contentMap.put("tupleTypes", tupleTypesJson.getBytes())
    }

    def "ConvertByteToObject: JSONからオブジェクトが生成できること"() {
        when:
        def content = contentMap.get(contractName)
        ContractAbi abi = ContractAbiConvertUtil.convertByteToObject(abiFormat, contractName, content)

        then:
        abi.getName() == contractName
        abi.getAddress() == address
        abi.getFunctionInfos().size() == functionSize
        ContractFunction function = abi.getFunction(functionName)
        function.getName() == functionName
        function.getInputParameterList().size() == inputSize

        where:
        contractName || abiFormat         | address                                      | functionSize | functionName      | inputSize | outputSize
        "Provider"   || AbiFormat.HARDHAT | "0xf17e6cA26072E0668d1a24F5Be91f45118B6E30c" | 20           | "addProviderRole" | 4         | 0
        "Validator"  || AbiFormat.TRUFFLE | "0x3C25BF12D614545d7535abf7Ca1014627140B7D1" | 13           | "hasValidatorID"  | 2         | 1
        "tupleTypes" || AbiFormat.TRUFFLE | "0x90774f99783f1238e1193cD7030FB3d686D0786E" | 4            | "getTupleTypes"   | 0         | 1
    }

    def "ConvertByteToObject: Tupleが正常に変換できること"() {
        when:
        def content = contentMap.get(contractName)
        ContractAbi abi = ContractAbiConvertUtil.convertByteToObject(AbiFormat.TRUFFLE, contractName, content)

        then:
        abi.getName() == contractName
        abi.getAddress() == address
        abi.getFunctionInfos().size() == functionSize
        ContractFunction function = abi.getFunction(functionName)
        function.getName() == functionName
        function.getInputParameterList().size() == inputSize

        where:
        contractName || address                                      | functionSize | functionName    | inputSize | outputSize | componentSize
        "tupleTypes" || "0x90774f99783f1238e1193cD7030FB3d686D0786E" | 4            | "getTupleTypes" | 0         | 1          | 3
    }
}

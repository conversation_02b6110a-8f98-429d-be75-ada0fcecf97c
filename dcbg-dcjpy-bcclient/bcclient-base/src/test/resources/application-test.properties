# サーバー設定
server.port=8081

# アプリケーション設定
## スケジュールに使用するスレッド数
spring.task.scheduling.pool.size=5

# ABIファイルのフォーマット指定
app.abiFormat=${ABI_FORMAT:hardhat}

## メインWebSocket
app.webSocketUriHost=${WEBSOCKET_URI_HOST:localhost}
app.webSocketUriPort=${WEBSOCKET_URI_PORT:8541}

## サブWebSocket
app.useSubWebSocket=${USE_SUB_WEBSOCKET:true}
app.subWebSocketUriHost=${SUB_WEBSOCKET_URI_HOST:localhost}
app.subWebSocketUriPort=${SUB_WEBSOCKET_URI_PORT:8541}

# DLT イベントの listen スケジュール間隔
app.subscriptionCheckInterval=${SUBSCRIPTION_CHECK_INTERVAL:3000}

app.requestTimeoutSec=${REQUEST_TIMEOUT_SEC:10000}
app.gasLimit=${GAS_LIMIT:6721975}

# send transaction 同時送信数の閾値
app.sending-threshold=${SENDING_THRESHOLD:200}

##########################################################################
# S3
##########################################################################
# 内部のコントラクタの ABI.json を保存するバケット名
s3.contractBucketName=${CONTRACT_BUCKET_NAME:abijson-local-bucket}
# 外部のコントラクタの ABI.json を保存するバケット名
s3.externalContractBucketName=${EXTERNAL_CONTRACT_BUCKET_NAME:external-abijson-local-bucket}
# ローカル環境への接続先。ポート番号はプログラム内で指定 (AWS 環境では空文字を設定すること)
s3.local-endpoint=${S3_LOCAL_ENDPOINT:http://localhost}

##########################################################################
# SQS
##########################################################################
# キュー名
sqs.queue-name=${SQS_QUEUE_NAME:dcjpy_bcclient_queue_send-transaction.fifo}
# 可視性タイムアウト時間 (秒)
sqs.visibility-timeout=${SQS_VISIBILITY_TIMEOUT:5}
# ポーリングメッセージ待機時間 (秒)
sqs.wait-time-seconds=${SQS_WAIT_TIME_SECONDS:1}
# ローカル環境への接続先 (AWS 環境では空文字を設定すること)
sqs.local-endpoint=${LOCAL_STACK_ENDPOINT:http://localhost:4566}

##########################################################################
# DynamoDB
##########################################################################
# ローカル環境への接続先 (AWS 環境では空文字を設定すること)
dynamodb.local-endpoint=${LOCAL_STACK_ENDPOINT:http://localhost:4566}
# transaction_queueテーブルのセカンダリインデクス名
dynamodb.transaction-queue-secondary-index=${TRANSACTION_QUEUE_SECONDARY_INDEX:transaction_hash_index}

# テスト設定
spring.main.allow-bean-definition-overriding=true

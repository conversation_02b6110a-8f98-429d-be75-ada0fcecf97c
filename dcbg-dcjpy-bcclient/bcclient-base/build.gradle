plugins {
    id 'org.springframework.boot' version '2.7.6'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'java'
    id 'groovy'
    id 'jacoco'

    id "eclipse"
    id "idea"
}

apply from: rootProject.file('build.common.gradle')

group = 'com.decurret_dcp'
version = '0.0.1-SNAPSHOT'

repositories {
    mavenCentral()
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
    testRuntime {
        // パッケージを排除しないとテスト実行時にExceptionが発生する:
        //   java.lang.IllegalArgumentException: LoggerFactory is not a Logback LoggerContext but Logback is on the classpath
        exclude group: 'ch.qos.logback', module: 'logback-classic'
    }
}

dependencies {
}
